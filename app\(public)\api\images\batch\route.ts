// app/api/images/batch/route.ts
import { NextRequest, NextResponse } from "next/server";
import { imageIndex } from '@/lib/image-index';


export async function POST(req: NextRequest) {
  try {
    // Rate limiting - removed unused ip variable
   
    const body = await req.json();
    const { productIds, material_numbers } = body;
    
    // Accept both parameter names for flexibility
    const ids = productIds || material_numbers;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json({ 
        error: "Invalid product IDs",
        code: "INVALID_INPUT" 
      }, { status: 400 });
    }

    // Validate input
    if (ids.length > 100) {
      return NextResponse.json({ 
        error: "Too many products requested. Maximum 100 allowed.",
        code: "BATCH_SIZE_EXCEEDED" 
      }, { status: 400 });
    }

    // Validate each ID
    const validIds = ids.filter(id => 
      typeof id === 'string' && 
      id.length > 0 && 
      id.length <= 50 && 
      /^[A-Za-z0-9_-]+$/.test(id) // Only alphanumeric, underscore, dash
    );

    if (validIds.length === 0) {
      return NextResponse.json({ 
        error: "No valid product IDs provided",
        code: "NO_VALID_IDS" 
      }, { status: 400 });
    }

    const results: Record<string, {
      mainImage: string | null;
      imageCount: number;
      hasImages: boolean;
    }> = {};

    // Get images for each product
    const startTime = Date.now();
    
    for (const productId of validIds) {
      const images = await imageIndex.getProductImages(productId);
      results[productId] = {
        mainImage: images.length > 0 ? `/api/images/${productId}/0` : null,
        imageCount: images.length,
        hasImages: images.length > 0
      };
    }

    const processingTime = Date.now() - startTime;

    return NextResponse.json({
      success: true,
      data: results,
      meta: {
        requestedCount: ids.length,
        validCount: validIds.length,
        processingTimeMs: processingTime,
        timestamp: new Date().toISOString()
      }
    }, {
      headers: {
        'Cache-Control': 'public, max-age=300, s-maxage=600', // 5min browser, 10min CDN
        'X-Processing-Time': processingTime.toString(),
        'X-Valid-Count': validIds.length.toString(),
      }
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;
    const errorCode = error && typeof error === 'object' && 'code' in error ? error.code : undefined;

    console.error('Batch images error:', {
      error: errorMessage,
      stack: errorStack,
      timestamp: new Date().toISOString()
    });

    if (errorCode === 'RATE_LIMITED') {
      return NextResponse.json({ 
        error: "Rate limit exceeded",
        code: "RATE_LIMITED" 
      }, { status: 429 });
    }

    return NextResponse.json({ 
      error: "Internal server error",
      code: "INTERNAL_ERROR" 
    }, { status: 500 });
  }
}