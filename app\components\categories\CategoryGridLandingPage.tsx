"use client"

import React from "react";
import { motion } from "framer-motion";
import { HeroBanner } from "@/app/getData/banners";
import Image from "next/image";


export default function CategoryGrid( { categories }: { categories: HeroBanner[] }) {

  if(categories.length === 0){
    return null
  }

  return (
    <section className="py-16">
      <div className="max-w-[1640px] mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="mb-12"
        >
          <h2 className="text-3xl font-bold mb-4">
            Cumpara dupa categorie
          </h2>
          <p className="text-lg text-gray-brand">
            Alege categoria dorita si gaseste produsele dorite
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {categories.map((category, index) => {
            if(!category.urlCat2){
              return "#"
            }
            if(!category.urlCat3){
              return "#"
            }
            return (
            <motion.a
              key={category.id}
              href={`/category/${encodeURIComponent(category.urlCat2) }?category3=${encodeURIComponent(category.urlCat3)}`} 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="group relative overflow-hidden transform transition-all duration-300 hover:-translate-y-1"
            >
              <div className="relative h-[300px] w-full overflow-hidden rounded-lg">
                <Image
                  src={`/api/images/banners/${encodeURIComponent(category.imageIdentifier)}`}
                  alt={category.title}
                  width={500}
                  height={500}
                  className="w-full h-full object-cover transform transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/40" />
                <div className="absolute inset-0 flex flex-col justify-end p-6">
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {category.title}
                  </h3>
                  <p className="text-sm text-white/70">
                    {category.subtitle}
                  </p>
                </div>
              </div>
            </motion.a>
          )})}
        </div>
      </div>
    </section>
  );
};

