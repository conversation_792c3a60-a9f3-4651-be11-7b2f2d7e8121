import type { 
  OrderStatus as PrismaOrderStatus,
  PaymentStatus as PrismaPaymentStatus,
  PaymentMethod as PrismaPaymentMethod,
  ShippingMethod as PrismaShippingMethod,
  ShipmentStatus as PrismaShipmentStatus,
  Showroom as PrismaShowroom,
  DiscountType as PrismaDiscountType,
  Prisma
} from "@/generated/prisma";
import { Decimal } from "@/generated/prisma/runtime/library";

// Order Product Interface for UI
export interface OrderProduct {
  id: string;
  name: string;
  notes?: string;
  notesToInvoice: boolean;
  oeCode: string;
  image: string;
  quantity: number;
  price: number;
  hasDiscount: boolean;
  discountPercentage: number | null;
  activeDiscountType: PrismaDiscountType | null;
  activeDiscountValue: number | null;
}

// Billing Address Interface
export interface OrderBillingAddress {
  fullName: string;
  companyName?: string;
  address: string;
  city: string;
  county: string;
  cui?: string;
  bank?: string;
  iban?: string;
}

// Shipping Address Interface
export interface OrderShippingAddress {
  fullName: string;
  address: string;
  city: string;
  county: string;
  phoneNumber: string;
  notes?: string;
}

// Shipping History Interface
export interface ShippingHistoryEvent {
  date: string;
  status: string;
  location: string;
  description: string;
}

// Order Interface for UI
export interface Order {
  id: string;
  orderNumber: string;
  date: string;
  amount: number;
  totalAmount: number;
  shippingCost: number;
  status: string;
  orderStatus: PrismaOrderStatus;
  paymentStatus: PrismaPaymentStatus;
  paymentMethod: PrismaPaymentMethod;
  shippingMethod: PrismaShippingMethod;
  shipmentStatus: PrismaShipmentStatus;
  showroom?: PrismaShowroom;
  items: OrderProduct[];
  billing?: OrderBillingAddress;
  shipping?: OrderShippingAddress;
  tracking?: string;
  estimatedDelivery?: string;
  currentLocation?: string;
  shippingHistory?: ShippingHistoryEvent[];
  notes?: string;
  invoiceAM?: string;
  // Timestamps
  placedAt: string;
  processedAt?: string;
  completedAt?: string;
  cancelledAt?: string;
  shippingProcessedAt?: string;
  shippedAt?: string;
  deliveredAt?: string;
  paidAt?: string;
  refundedAt?: string;
}

// Raw Order Item from Prisma (for data fetching)
export interface RawOrderItemFromPrisma {
  id: string;
  quantity: number;
  price: Decimal; // Prisma Decimal type
  notes?: string | null;
  notesToInvoice: boolean;
  product: {
    id: string;
    Material_Number: string;
    Description_Local: string | null;
    PretAM: Decimal | null; // Prisma Decimal type
    FinalPrice: Decimal | null; // Prisma Decimal type
    ImageUrl: string[];
    HasDiscount: boolean;
    discountPercentage: Decimal | null; // Prisma Decimal type
    activeDiscountType: PrismaDiscountType | null;
    activeDiscountValue: Decimal | null; // Prisma Decimal type
  };
}

export const orderSelect = {
  id: true,
  orderNumber: true,
  amount: true,
  shippingCost: true,
  totalAmount: true,
  invoiceAM: true,
  notes: true,
  orderStatus: true,
  paymentStatus: true,
  paymentMethod: true,
  shippingMethod: true,
  shipmentStatus: true,
  showroom: true,
  placedAt: true,
  createdAt: true,
  updatedAt: true,
  orderItems: {
    select: {
      id: true,
      quantity: true,
      price: true,
      notes: true,
      notesToInvoice: true,
      product: {
        select: {
          id: true,
          Material_Number: true,
          Net_Weight: true,
          Description_Local: true,
          PretAM: true,
          FinalPrice: true,
          ImageUrl: true,
          HasDiscount: true,
          discountPercentage: true,
          activeDiscountType: true,
          activeDiscountValue: true,
        },
      },
    },
  },
  billingAddress: {
    select: {
      fullName: true,
      companyName: true,
      address: true,
      city: true,
      county: true,
      cui: true,
      bank: true,
      iban: true,
    },
  },
  shippingAddress: {
    select: {
      fullName: true,
      address: true,
      city: true,
      county: true,
      phoneNumber: true,
      notes: true,
    },
  },
} as const satisfies Prisma.OrderSelect;

// 2) Derive the exact payload type from the select
// export type RawOrderFromPrisma = Prisma.OrderGetPayload<{ select: typeof orderSelect }>;


export interface OrdersResponse {
  orders: Order[];
  pagination: {
    total: number;
    pages: number;
    currentPage: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface OrderFilters {
  status?: PrismaOrderStatus | 'all'; // Use the enum from Prisma
  search?: string;
  page?: number;
  limit?: number;
  dateFrom?: string;
  dateTo?: string;
}

// Order Filters Interface
// export interface OrderFilters {
//   status?: string;
//   search?: string;
//   page?: number;
//   limit?: number;
//   dateFrom?: string;
//   dateTo?: string;
// }

// Order Status Mapping
export const ORDER_STATUS_LABELS: Record<PrismaOrderStatus, string> = {
  plasata: "Plasată",
  completa: "Completă",
  anulata: "Anulată",
  procesare: "În procesare",
  livrare: "În curs de livrare"
};

// Payment Status Mapping
export const PAYMENT_STATUS_LABELS: Record<PrismaPaymentStatus, string> = {
  asteptare: "Pending",
  succes: "Success",
  esuat: "Failed",
  rambursat: "Refunded",
  partial_rambursat: "Partially Refunded",
  contestat: "Disputed"
};

// Shipment Status Mapping
export const SHIPMENT_STATUS_LABELS: Record<PrismaShipmentStatus, string> = {
  asteptare: "Pending",
  prelucrare: "Processing",
  pregatit: "Ready",
  expediat: "Shipped",
  tranzit: "In Transit",
  livrat: "Delivered",
  esuat: "Failed",
  intors: "Returned",
  anulat: "Cancelled",
  partial: "Partially Shipped"
};

// Payment Method Mapping
export const PAYMENT_METHOD_LABELS: Record<PrismaPaymentMethod, string> = {
  ramburs: "Cash on Delivery",
  card: "Credit Card",
  transfer: "Bank Transfer",
  laTermen: "Payment Terms"
};

// Shipping Method Mapping
export const SHIPPING_METHOD_LABELS: Record<PrismaShippingMethod, string> = {
  curier: "Curier",
  showroom: "Ridicare din showroom",
  intern: "Transport intern"
};
