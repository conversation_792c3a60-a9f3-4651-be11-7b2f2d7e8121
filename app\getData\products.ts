"server-only"
import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { toSafeNumber } from "@/lib/utils"
import { productCodSchema } from "@/lib/zod"
import { ProductCardInterface, ProductPage } from "@/types/product"
import { imageIndex } from "@/lib/image-index";


export async function getFeaturedProducts(limit = 6): Promise<ProductCardInterface[]> {
  //const cacheKey = `featured-products:${limit}`;
  'use cache'
  try {
    // const productsFromDbOrCache = await getCachedData(
    //   cacheKey,
    //   async () => {
    //     return withRetry(() =>
    const productsFromDbOrCache = await withRetry(() =>
          prisma.product.findMany({
            where: { 
              IsOnLandingPage: true,
              isActive: true
             },
            take: limit,
            select: {
              id: true,
              Material_Number: true,
              Description_Local: true,
              PretAM: true,
              FinalPrice: true,
              ImageUrl: true,    
              categoryLevel3: { select: { name: true } },                       
              HasDiscount: true,
              discountPercentage: true,
              activeDiscountType: true,
              activeDiscountValue: true,
              //stockStatus: true,
              productClass: {
                select: {
                  vehicleModels: {
                    select: { vehicleModel: { select: { name: true } } },
                  },
                },
              },
            },
          })
        );
      
    // If the data from cache/db is null or not an array, return an empty array.
    if (!productsFromDbOrCache || !Array.isArray(productsFromDbOrCache)) {
        logger.warn('[getFeaturedProducts] No products found or data is malformed.');
        return [];
    }
  //console.log('[DEBUG] Raw products from DB:', productsFromDbOrCache);
    // The mapping logic remains largely the same, but it's now inside the try block.
    // Any unexpected error during mapping will be caught.
    return productsFromDbOrCache.map((p) => ({
      id: p.id,
      Material_Number: p.Material_Number,
      Description_Local: p.Description_Local,
      PretAM:   toSafeNumber(p.PretAM),
      FinalPrice: toSafeNumber(p.FinalPrice),
      ImageUrl: p.ImageUrl,

      categoryLevel3: p.categoryLevel3
        ? { name: p.categoryLevel3.name }
        : null,

      HasDiscount: p.HasDiscount,
      discountPercentage: toSafeNumber(p.discountPercentage),
      activeDiscountType: p.activeDiscountType,
      activeDiscountValue:  toSafeNumber(p.activeDiscountValue),

      productClass: p.productClass
        ? {
            vehicleModels: p.productClass.vehicleModels.map(vm => ({
              vehicleModel: { name: vm.vehicleModel.name }
            }))
          }
        : null,
    }));
  } catch (error) {
    logger.error(`[getFeaturedProducts] Error fetching products: ${error}`);
    return [];
  }
}

export async function getProductByMaterialNumber(material_number: string): Promise<ProductPage | null>{

  if (!material_number) {
    logger.warn('[getProductById] Called with an empty or null productId.');
    return null;
  }

  //validate with zod
  const parsed = productCodSchema.safeParse(material_number);
  if(!parsed.success) {
    logger.error("[getProductById] Invalid productCode:", parsed.error.format());
    return null;
  }

  const Material_Number = parsed.data

  try {
    const rawProduct = await withRetry(() =>
      prisma.product.findUnique({
        where: {
          isActive: true,
          Material_Number , // your param
        },
        select: {
          id: true,
          Material_Number: true,
          Net_Weight: true,
          Description_Local: true,
          Base_Unit_Of_Measur: true,
          Cross_Plant: true,
          New_Material: true,
          PretAM: true,
          FinalPrice: true,
          HasDiscount: true,
          activeDiscountType: true,
          activeDiscountValue: true,
          discountPercentage: true,
          ImageUrl: true,
          attributes: {
            select: {
              key: true,
              value: true,
            },
          },
          productClass: {
            select: {
              vehicleModels: {
                select: {
                  vehicleModel: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
              classCode: true,
              brand: {
                select: {
                  name: true,
                },
              },
            },
          },
          categoryLevel3: {
            select: {
              name: true,
            },
          },
        },
      })
    );

    if(!rawProduct){
      logger.warn(`[getProductById] Not found product for ${material_number}`)
      return null
    }

    const safeProduct: ProductPage = {
      id: rawProduct.id,
      Material_Number: rawProduct.Material_Number,
      Net_Weight: rawProduct.Net_Weight ?? '',
      Description_Local: rawProduct.Description_Local ,
      Base_Unit_Of_Measur: rawProduct.Base_Unit_Of_Measur ?? '',
      Cross_Plant: rawProduct.Cross_Plant,
      New_Material: rawProduct.New_Material,
      PretAM: toSafeNumber(rawProduct.PretAM),
      FinalPrice: toSafeNumber(rawProduct.FinalPrice),
      HasDiscount: rawProduct.HasDiscount,
      activeDiscountType: rawProduct.activeDiscountType,
      activeDiscountValue: toSafeNumber(rawProduct.activeDiscountValue),
      discountPercentage: toSafeNumber(rawProduct.discountPercentage),
      ImageUrl: rawProduct.ImageUrl,
      attributes: rawProduct.attributes,
      productClass: rawProduct.productClass,
      categoryLevel3: rawProduct.categoryLevel3,
      classCode: rawProduct.productClass?.classCode ?? '',
      brandName: rawProduct.productClass?.brand?.name ?? '',
    };
    
    return safeProduct;
  } catch (error) {
    logger.error(`[getProductById] Error fetching product: ${error}`);
    return null;
  }
}

// Search products (no caching for search - results vary too much)
export async function searchProducts(query: string, limit = 20) {
  if (!query || query.length < 2) return []
  
  return withRetry( () =>  prisma.product.findMany({
    where: {
      OR: [
        { Material_Number_normalized: { contains: query.toLowerCase() } },
        { Description_Local_normalized: { contains: query.toLowerCase() } }
      ],
      isActive: true
    },
    take: limit,
    select: {
      Material_Number: true,
      Description_Local: true,
      ImageUrl: true,
      FinalPrice: true
    }
  }))
}

export async function findProductImages(productName: string): Promise<string[]> {
return await imageIndex.getProductImages(productName);
}

export interface ProductImageData {
mainImage: string | null;
imageCount: number;
hasImages: boolean;
}

export interface BatchImageResponse {
success: boolean;
data: Record<string, ProductImageData>;
meta: {
requestedCount: number;
validCount: number;
processingTimeMs: number;
timestamp: string;
};
}

export async function getPicturesForProducts(
materialNumbers: string[]
): Promise<BatchImageResponse> {
try {
// Validate input
if (!Array.isArray(materialNumbers) || materialNumbers.length === 0) {
logger.error('Invalid material numbers provided');
return {
success: false,
data: {},
meta: {
requestedCount: 0,
validCount: 0,
processingTimeMs: 0,
timestamp: new Date().toISOString()
}
};
}


if (materialNumbers.length > 100) {
  logger.error('Too many products requested. Maximum 100 allowed.');
  return {
    success: false,
    data: {},
    meta: {
      requestedCount: materialNumbers.length,
      validCount: 0,
      processingTimeMs: 0,
      timestamp: new Date().toISOString()
    }
  };
}

// Validate each ID
const validIds = materialNumbers.filter(id => 
  typeof id === 'string' && 
  id.length > 0 && 
  id.length <= 50 && 
  /^[A-Za-z0-9_-]+$/.test(id)
);

if (validIds.length === 0) {
  logger.error('No valid material numbers provided');
  return {
    success: false,
    data: {},
    meta: {
      requestedCount: materialNumbers.length,
      validCount: 0,
      processingTimeMs: 0,
      timestamp: new Date().toISOString()
    }
  };
}

const results: Record<string, ProductImageData> = {};
const startTime = Date.now();

// Process in parallel for better performance
const imagePromises = validIds.map(async (productId) => {
  const images = await imageIndex.getProductImages(productId);
  return {
    productId,
    data: {
      mainImage: images.length > 0 ? `/api/images/${productId}/0` : null,
      imageCount: images.length,
      hasImages: images.length > 0
    }
  };
});

const imageResults = await Promise.all(imagePromises);

// Build results object
imageResults.forEach(({ productId, data }) => {
  results[productId] = data;
});

const processingTime = Date.now() - startTime;

return {
  success: true,
  data: results,
  meta: {
    requestedCount: materialNumbers.length,
    validCount: validIds.length,
    processingTimeMs: processingTime,
    timestamp: new Date().toISOString()
  }
};
} catch (error) {
logger.error('getPicturesForProducts error:', {
error,
materialNumbers: materialNumbers?.slice(0, 5), // Log first 5 for debugging
timestamp: new Date().toISOString()
});


return {
  success: false,
  data: {},
  meta: {
    requestedCount: materialNumbers?.length || 0,
    validCount: 0,
    processingTimeMs: 0,
    timestamp: new Date().toISOString()
  }
};
}
}


// export  async function findProductImages(productName: string): Promise<string[]> {
//   const BASE_DIR = process.env.PICTURES_BASE_PATH;
//   const ALLOWED_EXT = new Set([".jpg", ".jpeg", ".png", ".webp", ".gif", ".avif", ".bmp"]);

  
//   if (!BASE_DIR) return [];
  
//   try {
//     const files = await fsp.readdir(BASE_DIR);
//     const productImages = files.filter(file => {
//       const ext = path.extname(file).toLowerCase();
//       const nameWithoutExt = path.basename(file, ext);
      
//       return ALLOWED_EXT.has(ext) && 
//              (nameWithoutExt === productName || nameWithoutExt.startsWith(productName + '_'));
//     });

//     return productImages.sort((a, b) => {
//       const aName = path.basename(a, path.extname(a));
//       const bName = path.basename(b, path.extname(b));
      
//       if (aName === productName && bName !== productName) return -1;
//       if (bName === productName && aName !== productName) return 1;
      
//       return aName.localeCompare(bName);
//     });
//   } catch {
//     return [];
//   }
// }