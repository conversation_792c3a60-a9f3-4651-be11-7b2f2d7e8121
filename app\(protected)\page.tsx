
import { getCurrentDbUser } from "@/lib/auth";
import { redirect } from "next/navigation";
import { ProductCardInterface } from "@/types/product";
import { getPretSiStocBatch, getPricesFor4thBatch } from "@/lib/mssql/query";
import { getFeaturedProducts } from "../getData/products";
import { getCategorySectionLandingPageBanners, getHeroBanners } from "../getData/banners";
import ProductGrid from "../components/product/ProductGrid";
import HeroSection from "../components/banner/HeroSection";
import CategoryGrid from "../components/categories/CategoryGridLandingPage";

export default async function Home() {

  const user = await getCurrentDbUser()

  if (!user) {
    return redirect("/sign-in");
  }

  // 1 Determine if user gets special 4️⃣-level pricing
  const has4th =
    user.role.includes("fourLvlAdminAB") ||
    user.role.includes("fourLvlInregistratAB");

  // 2 Fetch user, main product, featured in parallel
  const [featured, heroBanners, categories] = await Promise.all([
    getFeaturedProducts(18),
    getHeroBanners(),
    getCategorySectionLandingPageBanners()
  ]);

  // 3 Kick off all remaining I/O in parallel:
  const featuredSkus = featured.map((p) => p.Material_Number);
  
  const [
    featuredStockMap,          // Record<string, PretSiStocAM[]>
    featured4thPriceBatch,     // GetPrice4LvlBatch[]
  ] = await Promise.all([
    getPretSiStocBatch(featuredSkus),
    has4th
      ? featuredSkus.length > 0 ? getPricesFor4thBatch(featuredSkus, user.userAM || "") : Promise.resolve([])
      : Promise.resolve([]),
  ]);

  // 4 Merge featured products
  const price4Map = new Map<string, number>(
    featured4thPriceBatch.map((p) => [p.itemno, p.pret])
  );

  const productsWithData: ProductCardInterface[] = featured.map((p) => {
    const batch = featuredStockMap[p.Material_Number] ?? [];
    const stock = batch.reduce((sum, x) => sum + x.stoc, 0);

    // only look up a 4th-level price if allowed
    const override = has4th
      ? price4Map.get(p.Material_Number)
      : undefined;

    return {
      ...p,
      stock,
      // fallback to DB price if no override (or user not allowed)
      displayPrice: override ?? p.FinalPrice,
    };
  });

  return (
    <>
       <HeroSection heroBanners={heroBanners} />

       <CategoryGrid  categories={categories} />   
 
       <ProductGrid products={productsWithData}    title="Produse recomandate"  description="Produse care te-ar putea interesa"  />    
    </>
  );
}


