{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":25404,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 12:19:04.472"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":25404,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 12:19:09.110"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":25404,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 12:19:11.617"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":25404,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 12:19:12.870"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":25404,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 12:19:13.508"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":420,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 13:08:15.915"}
{"level":"error","message":"[getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":420,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 13:08:15.917"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":420,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 13:08:20.564"}
{"level":"error","message":"[getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":420,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 13:08:20.564"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":420,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 13:09:18.138"}
{"level":"error","message":"[getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":420,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 13:09:18.139"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":420,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 13:27:12.496"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":420,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 13:27:12.502"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":420,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 13:27:17.496"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":420,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 13:28:18.941"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":44116,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 13:51:08.216"}
{"level":"error","message":"[getPretSiStoc] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStoc] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":44116,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 13:51:08.222"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":44116,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 13:51:08.250"}
{"level":"error","message":"[getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":44116,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 13:51:08.251"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":44116,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 13:51:17.562"}
{"level":"error","message":"[getPretSiStoc] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStoc] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":44116,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 13:51:17.564"}
{"level":"error","message":"[ReturnsRoute] Error loading returns for user cmelwhaxx00001ghx4mmv9sub: [ReturnsRoute] Error loading returns for user cmelwhaxx00001ghx4mmv9sub:","context":"app","environment":"development","hostname":"my-server-01","pid":21824,"error":{"name":"TypeError","message":"(0 , _app_getData_addresses__WEBPACK_IMPORTED_MODULE_3__.getActiveShowrooms) is not a function","stack":"TypeError: (0 , _app_getData_addresses__WEBPACK_IMPORTED_MODULE_3__.getActiveShowrooms) is not a function\n    at ReturnsRoute (webpack-internal:///(rsc)/./app/(protected)/account/returns/page.tsx:43:87)"},"timestamp":"2025-09-12 14:37:02.902"}
{"level":"error","message":"[ReturnsRoute] Error loading returns for user cmelwhaxx00001ghx4mmv9sub: [ReturnsRoute] Error loading returns for user cmelwhaxx00001ghx4mmv9sub:","context":"app","environment":"development","hostname":"my-server-01","pid":21824,"error":{"name":"TypeError","message":"(0 , _app_getData_addresses__WEBPACK_IMPORTED_MODULE_3__.getActiveShowrooms) is not a function","stack":"TypeError: (0 , _app_getData_addresses__WEBPACK_IMPORTED_MODULE_3__.getActiveShowrooms) is not a function\n    at ReturnsRoute (webpack-internal:///(rsc)/./app/(protected)/account/returns/page.tsx:43:87)"},"timestamp":"2025-09-12 14:37:10.485"}
{"level":"error","message":"[ReturnsRoute] Error loading returns for user cmelwhaxx00001ghx4mmv9sub: [ReturnsRoute] Error loading returns for user cmelwhaxx00001ghx4mmv9sub:","context":"app","environment":"development","hostname":"my-server-01","pid":21824,"error":{"name":"TypeError","message":"(0 , _app_getData_addresses__WEBPACK_IMPORTED_MODULE_3__.getActiveShowrooms) is not a function","stack":"TypeError: (0 , _app_getData_addresses__WEBPACK_IMPORTED_MODULE_3__.getActiveShowrooms) is not a function\n    at ReturnsRoute (webpack-internal:///(rsc)/./app/(protected)/account/returns/page.tsx:43:87)"},"timestamp":"2025-09-12 14:37:11.965"}
{"level":"error","message":"[createReturn] Error creating return: [createReturn] Error creating return:","context":"app","environment":"development","hostname":"my-server-01","pid":31288,"error":{"name":"PrismaClientKnownRequestError","message":"\nInvalid `prisma.orderPostPurchaseAction.create()` invocation:\n\n\nUnique constraint failed on the fields: (`returnId`)","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.orderPostPurchaseAction.create()` invocation:\n\n\nUnique constraint failed on the fields: (`returnId`)\n    at Xn.handleRequestError (webpack-internal:///(rsc)/./generated/prisma/runtime/library.js:7148:19)\n    at Xn.handleAndLogRequestError (webpack-internal:///(rsc)/./generated/prisma/runtime/library.js:7111:18)\n    at Xn.request (webpack-internal:///(rsc)/./generated/prisma/runtime/library.js:7092:18)\n    at async l (webpack-internal:///(rsc)/./generated/prisma/runtime/library.js:7760:25)\n    at async eval (webpack-internal:///(action-browser)/./app/actions/returns.ts:194:17)\n    at async Proxy._transactionWithCallback (webpack-internal:///(rsc)/./generated/prisma/runtime/library.js:7693:21)\n    at async createReturn (webpack-internal:///(action-browser)/./app/actions/returns.ts:152:24)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\compiled\\next-server\\app-page.runtime.dev.js:417:2449\n    at async handleAction (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\compiled\\next-server\\app-page.runtime.dev.js:416:21371)\n    at async renderToHTMLOrFlightImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\compiled\\next-server\\app-page.runtime.dev.js:422:27153)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\base-server.js:1655:34)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\base-server.js:1920:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\base-server.js:2408:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\base-server.js:2445:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\base-server.js:1008:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\base-server.js:900:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)"},"timestamp":"2025-09-12 15:14:53.574"}
{"level":"error","message":"[createReturn] Error creating return: [createReturn] Error creating return:","context":"app","environment":"development","hostname":"my-server-01","pid":31288,"error":{"name":"PrismaClientKnownRequestError","message":"\nInvalid `prisma.orderPostPurchaseAction.create()` invocation:\n\n\nUnique constraint failed on the fields: (`returnId`)","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.orderPostPurchaseAction.create()` invocation:\n\n\nUnique constraint failed on the fields: (`returnId`)\n    at Xn.handleRequestError (webpack-internal:///(rsc)/./generated/prisma/runtime/library.js:7148:19)\n    at Xn.handleAndLogRequestError (webpack-internal:///(rsc)/./generated/prisma/runtime/library.js:7111:18)\n    at Xn.request (webpack-internal:///(rsc)/./generated/prisma/runtime/library.js:7092:18)\n    at async l (webpack-internal:///(rsc)/./generated/prisma/runtime/library.js:7760:25)\n    at async eval (webpack-internal:///(action-browser)/./app/actions/returns.ts:194:17)\n    at async Proxy._transactionWithCallback (webpack-internal:///(rsc)/./generated/prisma/runtime/library.js:7693:21)\n    at async createReturn (webpack-internal:///(action-browser)/./app/actions/returns.ts:152:24)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\compiled\\next-server\\app-page.runtime.dev.js:417:2449\n    at async handleAction (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\compiled\\next-server\\app-page.runtime.dev.js:416:21371)\n    at async renderToHTMLOrFlightImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\compiled\\next-server\\app-page.runtime.dev.js:422:27153)\n    at async doRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\base-server.js:1655:34)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\base-server.js:1920:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\base-server.js:2408:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\base-server.js:2445:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\base-server.js:1008:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\next-server.js:305:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\base-server.js:900:17)\n    at async C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\lib\\router-server.js:237:21)\n    at async handleRequest (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\lib\\router-server.js:428:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\lib\\router-server.js:452:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\pieseabv-frontend\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)"},"timestamp":"2025-09-12 15:15:01.875"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":46932,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 15:25:47.228"}
{"level":"error","message":"[getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":46932,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 15:25:47.229"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":46932,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 15:25:49.918"}
{"level":"error","message":"[getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":46932,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 15:25:49.921"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":46932,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 15:25:50.028"}
{"level":"error","message":"[getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":46932,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 15:25:50.030"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":46932,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 15:26:02.305"}
{"level":"error","message":"[getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":46932,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 15:26:02.308"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":46932,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 15:26:20.200"}
{"level":"error","message":"[ShippingPage] Error loading shipping page: [ShippingPage] Error loading shipping page:","context":"app","environment":"production","hostname":"my-server-01","pid":45236,"error":{"name":"Error","message":"Dynamic server usage: Route /account/shipping couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error"},"timestamp":"2025-09-12 15:28:53.559"}
{"level":"error","message":"[CartRoute] Error fetching cart: Error: Dynamic server usage: Route /cart couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error [CartRoute] Error fetching cart: Error: Dynamic server usage: Route /cart couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error","context":"app","environment":"production","hostname":"my-server-01","pid":45236,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 15:28:54.600"}
{"level":"error","message":"[BillingPage] Error loading billing page: [BillingPage] Error loading billing page:","context":"app","environment":"production","hostname":"my-server-01","pid":42480,"error":{"name":"Error","message":"Dynamic server usage: Route /account/billing couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error"},"timestamp":"2025-09-12 15:28:54.862"}
{"level":"error","message":"[WishlistRoute] Error fetching wishlist: Error: Dynamic server usage: Route /account/wishlist couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error [WishlistRoute] Error fetching wishlist: Error: Dynamic server usage: Route /account/wishlist couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error","context":"app","environment":"production","hostname":"my-server-01","pid":45236,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 15:28:55.012"}
{"level":"error","message":"[AccountSettingsRoute] Error in account settings route: [AccountSettingsRoute] Error in account settings route:","context":"app","environment":"production","hostname":"my-server-01","pid":45236,"error":{"name":"Error","message":"Dynamic server usage: Route /account/settings couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error"},"timestamp":"2025-09-12 15:28:55.075"}
{"level":"error","message":"[AccountSettingsRoute] Error in account settings route: [AccountSettingsRoute] Error in account settings route:","context":"app","environment":"production","hostname":"my-server-01","pid":21644,"error":{"name":"Error","message":"Dynamic server usage: Route /account/settings couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error"},"timestamp":"2025-09-12 15:39:31.390"}
{"level":"error","message":"[BillingPage] Error loading billing page: [BillingPage] Error loading billing page:","context":"app","environment":"production","hostname":"my-server-01","pid":21644,"error":{"name":"Error","message":"Dynamic server usage: Route /account/billing couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error"},"timestamp":"2025-09-12 15:39:31.530"}
{"level":"error","message":"[ShippingPage] Error loading shipping page: [ShippingPage] Error loading shipping page:","context":"app","environment":"production","hostname":"my-server-01","pid":44352,"error":{"name":"Error","message":"Dynamic server usage: Route /account/shipping couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error"},"timestamp":"2025-09-12 15:39:32.252"}
{"level":"error","message":"[WishlistRoute] Error fetching wishlist: Error: Dynamic server usage: Route /account/wishlist couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error [WishlistRoute] Error fetching wishlist: Error: Dynamic server usage: Route /account/wishlist couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error","context":"app","environment":"production","hostname":"my-server-01","pid":21644,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 15:39:32.334"}
{"level":"error","message":"[CartRoute] Error fetching cart: Error: Dynamic server usage: Route /cart couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error [CartRoute] Error fetching cart: Error: Dynamic server usage: Route /cart couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error","context":"app","environment":"production","hostname":"my-server-01","pid":21644,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 15:39:32.412"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":36988,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 16:24:38.389"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":37052,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 16:40:12.437"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":25272,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 16:51:15.963"}
{"level":"error","message":"[getPriceFor4th] Error fetching price data: RequestError: Timeout: Request failed to complete in 15000ms [getPriceFor4th] Error fetching price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":25272,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 16:51:15.964"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":25272,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 17:24:12.703"}
{"level":"error","message":"[getPretSiStoc] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStoc] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":25272,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 17:24:12.705"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":25272,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 17:24:12.719"}
{"level":"error","message":"[getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStocBatch] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":25272,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 17:24:12.722"}
{"level":"error","message":"[mssql] Query failed [mssql] Query failed","context":"app","environment":"development","hostname":"my-server-01","pid":25272,"error":{"message":"[object Object]","type":"object"},"timestamp":"2025-09-12 17:24:14.226"}
{"level":"error","message":"[getPretSiStoc] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms [getPretSiStoc] Error fetching stock/price data: RequestError: Timeout: Request failed to complete in 15000ms","context":"app","environment":"development","hostname":"my-server-01","pid":25272,"error":{"message":"undefined","type":"undefined"},"timestamp":"2025-09-12 17:24:14.227"}
