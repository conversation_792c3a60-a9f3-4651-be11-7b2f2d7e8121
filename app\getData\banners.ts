"server-only"

import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger";

export interface HeroBanner {
  id: string;
  title: string;
  imageIdentifier: string;
  subtitle: string | null;
  imageFilename: string | null;
  mobileFileName: string | null;
  callToAction: string | null;
  buttonText: string | null;
  description: string | null;
  urlCat2: string | null;
  urlCat3: string | null;
  position: number;
  textAlignment: string | null;
}

//get the banners with HERO placement from prisma
export async function getHeroBanners(): Promise<HeroBanner[]> {
  try{
    const banners = await withRetry(() => prisma.banner.findMany({
      where: {
        placement: "HERO",
        isActive: true
      },
      select: {
        id: true,
        title: true,
        imageIdentifier: true,  
        subtitle: true,
        imageFilename: true,
        mobileFileName: true,
        callToAction: true,
        buttonText: true,
        description: true,
        urlCat2: true,
        urlCat3: true,
        position: true,
        textAlignment: true,
      },
      orderBy: {
        position: "asc"
      }
    }))

    if(banners.length === 0){
      logger.warn('[getHeroBanners] No Hero banners to return')
      return []
    }  

    return banners
  }catch(e){
    logger.error(`[getHeroBanners] Error trying to get the Hero Banners: ${e}`)
    return []
  }
}

//get the banners with CATEGORY_SECTION_LANDING_PAGE placement from prisma
export async function getCategorySectionLandingPageBanners(): Promise<HeroBanner[]> {
  try{
    const banners = await withRetry(() => prisma.banner.findMany({
      where: {
        placement: "CATEGORY_SECTION_LANDING_PAGE",
        isActive: true
      },
      select: {
        id: true,
        title: true,
        imageIdentifier: true,
        subtitle: true,
        imageFilename: true,
        mobileFileName: true,
        callToAction: true,
        buttonText: true,
        description: true,
        urlCat2: true,
        urlCat3: true,
        position: true,
        textAlignment: true,
      },
      orderBy: {
        position: "asc"
      }
    }))

    if(banners.length === 0){
      logger.warn('[getCategorySectionLandingPageBanners] No CategorySection banners to return')
      return []
    }

    return banners
  }catch(e){
            logger.error(`[getCategorySectionLandingPageBanners] Error trying to get the CategorySection : ${e}`)
    return []
  }
}   