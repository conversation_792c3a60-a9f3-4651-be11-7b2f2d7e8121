// import winston from 'winston';
// import DailyRotateFile from 'winston-daily-rotate-file';
// import path from 'path';
// import { format } from 'winston';

// type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// interface BaseLogOptions { 
//   level?: LogLevel; 
//   context?: string; 
//   userId?: string;
//   requestId?: string;
//   correlationId?: string;
//   sessionId?: string;
// }

// type LogOptions = BaseLogOptions & Record<string, unknown>;

// interface LoggerConfig {
//   logDir: string;
//   maxFileSize: string;
//   maxFiles: string;
//   enableConsole: boolean;
//   enableFileLogging: boolean;
//   enableErrorFile: boolean;
//   enableAuditFile: boolean;
//   compressionEnabled: boolean;
//   logLevel: LogLevel;
// }

// class EnterpriseLogger {
//   private static instance: EnterpriseLogger;
//   private readonly logger: winston.Logger;
//   private readonly auditLogger: winston.Logger;
//   private readonly config: LoggerConfig;
//   private readonly isProduction: boolean;

//   private constructor() {
//     this.isProduction = process.env.NODE_ENV === 'production';
//     this.config = this.loadConfig();
//     this.logger = this.createMainLogger();
//     this.auditLogger = this.createAuditLogger();
//     this.setupErrorHandling();
//   }

//   public static getInstance(): EnterpriseLogger {
//     if (!EnterpriseLogger.instance) {
//       EnterpriseLogger.instance = new EnterpriseLogger();
//     }
//     return EnterpriseLogger.instance;
//   }

//   private loadConfig(): LoggerConfig {
//     return {
//       logDir: process.env.LOG_DIR || './logs',
//       maxFileSize: process.env.MAX_LOG_FILE_SIZE || '100m',
//       maxFiles: process.env.MAX_LOG_FILES || '30d',
//       enableConsole: process.env.ENABLE_CONSOLE_LOGS !== 'false',
//       enableFileLogging: process.env.ENABLE_FILE_LOGS !== 'false',
//       enableErrorFile: process.env.ENABLE_ERROR_FILE !== 'false',
//       enableAuditFile: process.env.ENABLE_AUDIT_FILE !== 'false',
//       compressionEnabled: process.env.ENABLE_LOG_COMPRESSION !== 'false',
//       logLevel: (process.env.LOG_LEVEL as LogLevel) || (this.isProduction ? 'info' : 'debug'),
//     };
//   }

//   private createSecureFormat() {
//     return format.combine(
//       format.timestamp({
//         format: 'YYYY-MM-DD HH:mm:ss.SSS'
//       }),
//       format.errors({ stack: !this.isProduction }), // Hide stack traces in production
//       format.json(),
//       format.printf((info) => {
//         // Sanitize sensitive data
//         const sanitized = this.sanitizeLogData(info);
//         return JSON.stringify(sanitized);
//       })
//     );
//   }

//   private createDevelopmentFormat() {
//     return format.combine(
//       format.timestamp({
//         format: 'YYYY-MM-DD HH:mm:ss.SSS'
//       }),
//       format.errors({ stack: true }),
//       format.colorize(),
//       format.printf((info) => {
//         const { timestamp, level, message, context = 'app', ...meta } = info;
//         const metaStr = Object.keys(meta).length ? `\n${JSON.stringify(meta, null, 2)}` : '';
//         return `[${timestamp}] [${level}] [${context}] ${message}${metaStr}`;
//       })
//     );
//   }

//   private createMainLogger(): winston.Logger {
//     const transports: winston.transport[] = [];

//     // Console transport
//     if (this.config.enableConsole) {
//       transports.push(
//         new winston.transports.Console({
//           level: this.config.logLevel,
//           format: this.isProduction ? this.createSecureFormat() : this.createDevelopmentFormat(),
//           handleExceptions: true,
//           handleRejections: true,
//         })
//       );
//     }

//     // File transports
//     if (this.config.enableFileLogging) {
//       // Combined logs
//       transports.push(
//         new DailyRotateFile({
//           filename: path.join(this.config.logDir, 'application-%DATE%.log'),
//           datePattern: 'YYYY-MM-DD',
//           maxSize: this.config.maxFileSize,
//           maxFiles: this.config.maxFiles,
//           format: this.createSecureFormat(),
//           level: this.config.logLevel,
//           handleExceptions: true,
//           handleRejections: true,
//           zippedArchive: this.config.compressionEnabled,
//           createSymlink: true,
//           symlinkName: 'application-current.log',
//         })
//       );

//       // Separate error file
//       if (this.config.enableErrorFile) {
//         transports.push(
//           new DailyRotateFile({
//             filename: path.join(this.config.logDir, 'error-%DATE%.log'),
//             datePattern: 'YYYY-MM-DD',
//             maxSize: this.config.maxFileSize,
//             maxFiles: this.config.maxFiles,
//             format: this.createSecureFormat(),
//             level: 'error',
//             handleExceptions: true,
//             handleRejections: true,
//             zippedArchive: this.config.compressionEnabled,
//             createSymlink: true,
//             symlinkName: 'error-current.log',
//           })
//         );
//       }
//     }

//     return winston.createLogger({
//       level: this.config.logLevel,
//       transports,
//       exitOnError: false,
//       silent: false,
//     });
//   }

//   private createAuditLogger(): winston.Logger {
//     if (!this.config.enableAuditFile || !this.config.enableFileLogging) {
//       return winston.createLogger({ silent: true });
//     }

//     return winston.createLogger({
//       level: 'info',
//       format: format.combine(
//         format.timestamp(),
//         format.json(),
//         format.printf((info) => {
//           const sanitized = this.sanitizeLogData(info);
//           return JSON.stringify(sanitized);
//         })
//       ),
//       transports: [
//         new DailyRotateFile({
//           filename: path.join(this.config.logDir, 'audit-%DATE%.log'),
//           datePattern: 'YYYY-MM-DD',
//           maxSize: this.config.maxFileSize,
//           maxFiles: '365d', // Keep audit logs longer
//           zippedArchive: this.config.compressionEnabled,
//           createSymlink: true,
//           symlinkName: 'audit-current.log',
//         }),
//       ],
//       exitOnError: false,
//     });
//   }

//   private setupErrorHandling(): void {
//     // Handle logger errors
//     this.logger.on('error', (error) => {
//       console.error('Logger error:', error);
//     });

//     this.auditLogger.on('error', (error) => {
//       console.error('Audit logger error:', error);
//     });

//     // Handle uncaught exceptions and rejections
//     process.on('uncaughtException', (error) => {
//       this.logger.error('Uncaught Exception', {
//         error: this.formatError(error),
//         context: 'process',
//         severity: 'critical',
//       });
      
//       // Give logger time to write before exiting
//       setTimeout(() => {
//         process.exit(1);
//       }, 1000);
//     });

//     process.on('unhandledRejection', (reason, promise) => {
//       this.logger.error('Unhandled Rejection', {
//         reason: this.formatError(reason),
//         promise: promise.toString(),
//         context: 'process',
//         severity: 'critical',
//       });
//     });

//     // Graceful shutdown
//     const gracefulShutdown = () => {
//       this.logger.info('Shutting down gracefully', { context: 'process' });
      
//       // Close all transports
//       this.logger.close();
//       this.auditLogger.close();
      
//       setTimeout(() => {
//         process.exit(0);
//       }, 1000);
//     };

//     process.on('SIGTERM', gracefulShutdown);
//     process.on('SIGINT', gracefulShutdown);
//   }

//   private sanitizeLogData(data: unknown): unknown {
//     if (typeof data !== 'object' || data === null) {
//       return data;
//     }

//     const sensitiveKeys = [
//       'password', 'passwd', 'pwd',
//       'secret', 'token', 'key', 'apikey', 'api_key',
//       'authorization', 'auth', 'bearer',
//       'cookie', 'session', 'csrf',
//       'ssn', 'social_security_number',
//       'credit_card', 'creditcard', 'cc_number',
//       'pin', 'cvv', 'cvc',
//       'private_key', 'privatekey',
//       'access_token', 'refresh_token',
//     ];

//     const sanitized = Array.isArray(data) ? [] : {};

//     for (const [key, value] of Object.entries(data)) {
//       const lowerKey = key.toLowerCase();
      
//       if (sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))) {
//         (sanitized as Record<string, unknown>)[key] = '[REDACTED]';
//       } else if (typeof value === 'object' && value !== null) {
//         (sanitized as Record<string, unknown>)[key] = this.sanitizeLogData(value);
//       } else {
//         (sanitized as Record<string, unknown>)[key] = value;
//       }
//     }

//     return sanitized;
//   }

//   private formatError(error: unknown): object {
//     if (error instanceof Error) {
//       return {
//         name: error.name,
//         message: error.message,
//         stack: this.isProduction ? undefined : error.stack,
//         cause: error.cause,
//       };
//     }

//     return {
//       message: String(error),
//       type: typeof error,
//     };
//   }

//   private enrichLogData(level: LogLevel, message: string, options?: LogOptions): object {
//     const baseData = {
//       level,
//       message,
//       context: options?.context || 'app',
//       environment: process.env.NODE_ENV || 'development',
//       hostname: process.env.HOSTNAME || 'unknown',
//       pid: process.pid,
//       ...(options?.userId && { userId: options.userId }),
//       ...(options?.requestId && { requestId: options.requestId }),
//       ...(options?.correlationId && { correlationId: options.correlationId }),
//       ...(options?.sessionId && { sessionId: options.sessionId }),
//     };

//     // Add additional options, excluding base properties
//     if (options) {
//       const { level, context, userId, requestId, correlationId, sessionId, ...additionalData } = options;
//       // Explicitly ignore these extracted properties
//       void level; void context; void userId; void requestId; void correlationId; void sessionId;
//       Object.assign(baseData, additionalData);
//     }

//     return baseData;
//   }

//   public debug(message: string, options?: LogOptions): void {
//     const logData = this.enrichLogData('debug', message, options);
//     this.logger.debug(message, logData);
//   }

//   public info(message: string, options?: LogOptions): void {
//     const logData = this.enrichLogData('info', message, options);
//     this.logger.info(message, logData);
//   }

//   public warn(message: string, options?: LogOptions): void {
//     const logData = this.enrichLogData('warn', message, options);
//     this.logger.warn(message, logData);
//   }

//   public error(message: string, error?: Error | unknown, options?: LogOptions): void {
//     const logData = this.enrichLogData('error', message, {
//       ...options,
//       error: this.formatError(error),
//     });
//     this.logger.error(message, logData);
//   }

//   // Security audit logging
//   public audit(action: string, options: LogOptions & { 
//     actor?: string; 
//     resource?: string; 
//     outcome: 'success' | 'failure';
//     severity?: 'low' | 'medium' | 'high' | 'critical';
//   }): void {
//     const auditData = {
//       action,
//       actor: options.actor || options.userId || 'system',
//       resource: options.resource,
//       outcome: options.outcome,
//       severity: options.severity || 'medium',
//       timestamp: new Date().toISOString(),
//       ...this.enrichLogData('info', `Audit: ${action}`, options),
//     };

//     this.auditLogger.info(`Audit: ${action}`, auditData);
    
//     // Also log to main logger for high severity events
//     if (options.severity === 'high' || options.severity === 'critical') {
//       this.logger.warn(`Security Audit: ${action}`, auditData);
//     }
//   }

//   // Performance logging
//   public performance(operation: string, duration: number, options?: LogOptions): void {
//     const perfData = this.enrichLogData('info', `Performance: ${operation}`, {
//       ...options,
//       operation,
//       duration,
//       unit: 'ms',
//     });

//     this.logger.info(`Performance: ${operation} took ${duration}ms`, perfData);
//   }

//   // Context-aware logging
//   public withContext(context: Partial<LogOptions>) {
//     return {
//       debug: (message: string, options?: LogOptions) => 
//         this.debug(message, { ...context, ...options }),
//       info: (message: string, options?: LogOptions) => 
//         this.info(message, { ...context, ...options }),
//       warn: (message: string, options?: LogOptions) => 
//         this.warn(message, { ...context, ...options }),
//       error: (message: string, error?: Error | unknown, options?: LogOptions) => 
//         this.error(message, error, { ...context, ...options }),
//       audit: (action: string, options: Parameters<typeof this.audit>[1]) =>
//         this.audit(action, { ...context, ...options }),
//       performance: (operation: string, duration: number, options?: LogOptions) =>
//         this.performance(operation, duration, { ...context, ...options }),
//     };
//   }

//   // Health check for monitoring
//   public getLoggerHealth(): { status: 'healthy' | 'degraded' | 'unhealthy'; details: unknown } {
//     try {
//       const transports = this.logger.transports;
//       const healthyTransports = transports.filter(t => !t.silent);
      
//       return {
//         status: healthyTransports.length > 0 ? 'healthy' : 'degraded',
//         details: {
//           totalTransports: transports.length,
//           healthyTransports: healthyTransports.length,
//           logLevel: this.config.logLevel,
//           isProduction: this.isProduction,
//         }
//       };
//     } catch (error) {
//       return {
//         status: 'unhealthy',
//         details: { error: this.formatError(error) }
//       };
//     }
//   }
// }

// export const logger = EnterpriseLogger.getInstance();

// // Export types for external use
// export type { LogLevel, LogOptions };


type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// Define specific types for known properties
interface BaseLogOptions { 
  level?: LogLevel; 
  context?: string; 
  userId?: string;
  requestId?: string;
}

// Use a generic type for additional properties
type LogOptions = BaseLogOptions & Record<string, unknown>;

class Logger {
  private static instance: Logger;
  private readonly logLevel: LogLevel;
  private readonly isProduction: boolean;

  private constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    // Set minimum log level for production
    this.logLevel = this.isProduction 
      ? (process.env.LOG_LEVEL as LogLevel) || 'info'
      : 'debug';
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public debug(message: string, options?: LogOptions): void {
    if (this.shouldLog('debug')) {
      this.log('debug', message, options);
    }
  }

  public info(message: string, options?: LogOptions): void {
    if (this.shouldLog('info')) {
      this.log('info', message, options);
    }
  }

  public warn(message: string, options?: LogOptions): void {
    if (this.shouldLog('warn')) {
      this.log('warn', message, options);
    }
  }

  public error(message: string, error?: Error | unknown, options?: LogOptions): void {
    if (this.shouldLog('error')) {
      this.log('error', message, { 
        ...options, 
        error: this.formatError(error) 
      });
    }
  }

  // Add request context helper
  public withContext(context: Partial<LogOptions>) {
    return {
      debug: (message: string, options?: LogOptions) => 
        this.debug(message, { ...context, ...options }),
      info: (message: string, options?: LogOptions) => 
        this.info(message, { ...context, ...options }),
      warn: (message: string, options?: LogOptions) => 
        this.warn(message, { ...context, ...options }),
      error: (message: string, error?: Error | unknown, options?: LogOptions) => 
        this.error(message, error, { ...context, ...options }),
    };
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(this.logLevel);
    const messageLevelIndex = levels.indexOf(level);
    return messageLevelIndex >= currentLevelIndex;
  }

  private log(level: LogLevel, message: string, options?: LogOptions): void {
    const timestamp = new Date().toISOString();
    const context = options?.context || 'app';

    if (this.isProduction) {
      // Optimized JSON format for Vercel
      const logData = {
        timestamp,
        level,
        message,
        context,
        ...(options?.userId && { userId: options.userId }),
        ...(options?.requestId && { requestId: options.requestId }),
        ...this.sanitizeOptions(options),
      };
      
      // Type assertion for console methods
      const consoleMethod = console[level] as (...args: unknown[]) => void;
      consoleMethod(JSON.stringify(logData));
    } else {
      // Readable format for development
      const extras = options ? ` ${JSON.stringify(options, null, 2)}` : '';
      const consoleMethod = console[level] as (...args: unknown[]) => void;
      consoleMethod(`[${timestamp}] [${level.toUpperCase()}] [${context}] ${message}${extras}`);
    }
  }

  private formatError(error?: Error | unknown): object | undefined {
    if (!error) return undefined;

    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: this.isProduction ? undefined : error.stack, // Hide stack in prod logs
        cause: error.cause,
      };
    }

    // Handle non-Error objects
    return {
      message: String(error),
      type: typeof error,
    };
  }

  private sanitizeOptions(options?: LogOptions): Record<string, unknown> {
    if (!options) return {};
    
    // Create a new object without sensitive keys
    const sanitized: Record<string, unknown> = {};
    const sensitiveKeys = ['level', 'context', 'error', 'password', 'token', 'secret'];
    
    for (const [key, value] of Object.entries(options)) {
      if (!sensitiveKeys.includes(key)) {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }
}

export const logger = Logger.getInstance();

// type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// interface LogOptions { 
//   level?: LogLevel; 
//   context?: string; 
//   userId?: string;
//   requestId?: string;
//   [key: string]: any; 
// }

// class Logger {
//   private static instance: Logger;
//   private readonly logLevel: LogLevel;
//   private readonly isProduction: boolean;

//   private constructor() {
//     this.isProduction = process.env.NODE_ENV === 'production';
//     // Set minimum log level for production
//     this.logLevel = this.isProduction 
//       ? (process.env.LOG_LEVEL as LogLevel) || 'info'
//       : 'debug';
//   }

//   public static getInstance(): Logger {
//     if (!Logger.instance) {
//       Logger.instance = new Logger();
//     }
//     return Logger.instance;
//   }

//   public debug(message: string, options?: LogOptions): void {
//     if (this.shouldLog('debug')) {
//       this.log('debug', message, options);
//     }
//   }

//   public info(message: string, options?: LogOptions): void {
//     if (this.shouldLog('info')) {
//       this.log('info', message, options);
//     }
//   }

//   public warn(message: string, options?: LogOptions): void {
//     if (this.shouldLog('warn')) {
//       this.log('warn', message, options);
//     }
//   }

//   public error(message: string, error?: Error | unknown, options?: LogOptions): void {
//     if (this.shouldLog('error')) {
//       this.log('error', message, { 
//         ...options, 
//         error: this.formatError(error) 
//       });
//     }
//   }

//   // Add request context helper
//   public withContext(context: Partial<LogOptions>) {
//     return {
//       debug: (message: string, options?: LogOptions) => 
//         this.debug(message, { ...context, ...options }),
//       info: (message: string, options?: LogOptions) => 
//         this.info(message, { ...context, ...options }),
//       warn: (message: string, options?: LogOptions) => 
//         this.warn(message, { ...context, ...options }),
//       error: (message: string, error?: Error | unknown, options?: LogOptions) => 
//         this.error(message, error, { ...context, ...options }),
//     };
//   }

//   private shouldLog(level: LogLevel): boolean {
//     const levels = ['debug', 'info', 'warn', 'error'];
//     const currentLevelIndex = levels.indexOf(this.logLevel);
//     const messageLevelIndex = levels.indexOf(level);
//     return messageLevelIndex >= currentLevelIndex;
//   }

//   private log(level: LogLevel, message: string, options?: LogOptions): void {
//     const timestamp = new Date().toISOString();
//     const context = options?.context || 'app';

//     if (this.isProduction) {
//       // Optimized JSON format for Vercel
//       const logData = {
//         timestamp,
//         level,
//         message,
//         context,
//         ...(options?.userId && { userId: options.userId }),
//         ...(options?.requestId && { requestId: options.requestId }),
//         ...this.sanitizeOptions(options),
//       };
      
//       // Vercel captures this automatically
//       console[level](JSON.stringify(logData));
//     } else {
//       // Readable format for development
//       const extras = options ? ` ${JSON.stringify(options, null, 2)}` : '';
//       console[level](`[${timestamp}] [${level.toUpperCase()}] [${context}] ${message}${extras}`);
//     }
//   }

//   private formatError(error?: Error | unknown): object | undefined {
//     if (!error) return undefined;

//     if (error instanceof Error) {
//       return {
//         name: error.name,
//         message: error.message,
//         stack: this.isProduction ? undefined : error.stack, // Hide stack in prod logs
//         cause: error.cause,
//       };
//     }

//     // Handle non-Error objects
//     return {
//       message: String(error),
//       type: typeof error,
//     };
//   }

//   private sanitizeOptions(options?: LogOptions): object {
//     if (!options) return {};
    
//     // Remove internal properties and sensitive data
//     const { level, context, error, password, token, secret, ...safe } = options;
//     return safe;
//   }
// }

// export const logger = Logger.getInstance();


//simpler logger
// type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// interface LogOptions {
//   level?: LogLevel;
//   context?: string;
//   [key: string]: any;
// }

// class Logger {
//   private static instance: Logger;
  
//   private constructor() {}
  
//   public static getInstance(): Logger {
//     if (!Logger.instance) {
//       Logger.instance = new Logger();
//     }
//     return Logger.instance;
//   }
  
//   public debug(message: string, options?: LogOptions): void {
//     this.log('debug', message, options);
//   }
  
//   public info(message: string, options?: LogOptions): void {
//     this.log('info', message, options);
//   }
  
//   public warn(message: string, options?: LogOptions): void {
//     this.log('warn', message, options);
//   }
  
//   public error(message: string, error?: Error, options?: LogOptions): void {
//     this.log('error', message, { ...options, error: this.formatError(error) });
//   }
  
//   private log(level: LogLevel, message: string, options?: LogOptions): void {
//     const timestamp = new Date().toISOString();
//     const context = options?.context || 'app';
    
//     // In production, you might want to use a proper logging service
//     if (process.env.NODE_ENV === 'production') {
//       // Format for structured logging
//       const logData = {
//         timestamp,
//         level,
//         message,
//         context,
//         ...options,
//       };
      
//       // In production, you might send this to a logging service
//       console[level](JSON.stringify(logData));
//     } else {
//       // More readable format for development
//       const extras = options ? ` ${JSON.stringify(options)}` : '';
//       console[level](`[${timestamp}] [${level.toUpperCase()}] [${context}] ${message}${extras}`);
//     }
//   }
  
//   private formatError(error?: Error): object | undefined {
//     if (!error) return undefined;
    
//     return {
//       name: error.name,
//       message: error.message,
//       stack: error.stack,
//       cause: error.cause,
//     };
//   }
// }

// export const logger = Logger.getInstance();