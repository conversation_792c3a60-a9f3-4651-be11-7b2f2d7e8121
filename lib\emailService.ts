import nodemailer from 'nodemailer';
import { logger } from './logger';
import { OrderDataEmail } from '@/types/email';
import { ServiceEmailData } from '@/types/services';
import { formatPriceRON } from './utils';
import { formatDate } from './order-utils';

const createTransport = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT!),
    secure: process.env.SMTP_PORT === '465', // true for 465, false for other ports like 587
    auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
    },
    // Production settings
    pool: true, // use pooled connection
    maxConnections: 5,
    maxMessages: 100,
    rateDelta: 20000, // 20 seconds
    rateLimit: 5, // max 5 emails per rateDelta
});

// Email validation function
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Order confirmation email template
const getOrderConfirmationTemplate = (orderData: OrderDataEmail) => {
  const { orderNumber, customerName, items, total, shippingAddress } = orderData;
  
  return {
    subject: `Cerere înregistrată #${orderNumber} - Automobile Bavaria`,
    html: `
<!DOCTYPE html>
<html lang="ro">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title>Actualizare comandă</title>
  <style>
    html,body { margin:0; padding:0; background:#f5f6f8; }
    table { border-collapse: collapse; }
    img { border:0; outline:none; text-decoration:none; -ms-interpolation-mode:bicubic; }
    a { color: inherit; text-decoration: none; }
    @media (max-width: 600px) {
      .container { width:100% !important; }
      .px { padding-left:20px !important; padding-right:20px !important; }
      .py { padding-top:24px !important; padding-bottom:24px !important; }
    }
  </style>
</head>
<body style="margin:0;padding:0;background:#f5f6f8;">
  <!-- Preheader (hidden in inbox preview) -->
  <span style="display:none!important;opacity:0;color:transparent;visibility:hidden;mso-hide:all;">
    Comanda ta a fost plasată cu succes și este în așteptarea confirmării și a procesării.
  </span>

  <table role="presentation" width="100%" bgcolor="#f5f6f8">
    <tr>
      <td align="center" style="padding:32px 16px;">
        <table role="presentation" width="600" class="container" style="width:600px;max-width:600px;background:#ffffff;border-radius:16px;overflow:hidden;box-shadow:0 8px 30px rgba(16,24,40,0.08);">
          <!-- Header -->
          <tr>
            <td style="background:#0066B1;padding:32px 40px;color:#ffffff;text-align:center;">
              <div style="font-family:Inter,'Segoe UI','Helvetica Neue',Arial,sans-serif;font-size:26px;font-weight:800;letter-spacing:0.3px;">
                AUTOMOBILE BAVARIA
              </div>
              <div style="margin-top:12px;display:inline-block;border:1px solid rgba(255,255,255,0.35);border-radius:999px;padding:8px 16px;font-family:Inter,'Segoe UI','Helvetica Neue',Arial,sans-serif;font-weight:600;font-size:13px;letter-spacing:0.3px;">
                Comandă Înregistrată
              </div>
              <div style="margin-top:12px;font-family:Inter,'Segoe UI','Helvetica Neue',Arial,sans-serif;font-size:18px;line-height:1.4;font-weight:500;opacity:0.95;">
                Comanda ta a fost plasată cu succes și este în așteptarea confirmării și a procesării.
              </div>
            </td>
          </tr>

          <!-- Body -->
          <tr>
            <td class="px py" style="padding:32px 40px;font-family:Inter,'Segoe UI','Helvetica Neue',Arial,sans-serif;color:#101828;">
              <p style="font-size:17px;line-height:1.7;margin:0 0 20px 0;">Bună ziua <strong>${customerName}</strong>,</p>

              <!-- Order summary -->
              <table role="presentation" width="100%" style="background:#fafbfc;border:1px solid #eef0f3;border-radius:14px;">
                <tr>
                  <td style="padding:20px 20px 12px 20px;">
                    <table role="presentation" width="100%">
                      <tr>
                        <td style="font-size:16px;font-weight:700;color:#0066B1;">Comanda #${orderNumber}</td>
                        <td align="right" style="font-size:13px;color:#667085;">${formatDate(new Date().toISOString())}</td>
                      </tr>
                    </table>
                  </td>
                </tr>

                <!-- Items -->
                <tr>
                  <td style="padding:4px 20px 6px 20px;">
                    <div style="font-size:15px;font-weight:700;color:#101828;margin:8px 0 6px 0;">Produse comandate</div>
                  </td>
                </tr>

                ${items.map(item => `
                  <tr>
                    <td style="padding:0 20px 10px 20px;">
                      <table role="presentation" width="100%" style="background:#ffffff;border:1px solid #eef0f3;border-radius:10px;">
                        <tr>
                          <td style="padding:14px 16px;">
                            <table role="presentation" width="100%">
                              <tr>
                                <td style="font-size:14px;color:#101828;">
                                  <div style="font-weight:600;">${item.name}</div>
                                  <div style="color:#667085;font-size:12px;margin-top:2px;">Cod: ${item.code} • Cantitate: ${item.quantity}</div>
                                </td>
                                <td align="right" style="white-space:nowrap;font-weight:700;color:#027a48;font-size:14px;">
                                  ${formatPriceRON(item.quantity * item.price)}
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                `).join('')}

                <!-- Total -->
                <tr>
                  <td style="padding:8px 20px 20px 20px;">
                    <table role="presentation" width="100%" style="background:#0066B1;border-radius:10px;">
                      <tr>
                        <td align="center" style="padding:16px 20px;">
                          <div style="color:#ffffff;font-size:16px;font-weight:800;letter-spacing:0.2px;">Total: ${formatPriceRON(total)}</div>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
              
              ${shippingAddress ? `
              <div class="shipping-section">
                <h3 class="section-title">🚚 Adresa de Livrare</h3>
                <div class="shipping-address">
                  <strong>${shippingAddress.street}</strong><br>
                  ${shippingAddress.city}, ${shippingAddress.state} ${shippingAddress.zipCode}<br>
                  ${shippingAddress.country}
                </div>
              </div>
              ` : ''}
              
            <!-- Next steps -->
              <table role="presentation" width="100%" style="margin-top:24px;background:#f0f9f2;border:1px solid #cfe9d6;border-left:4px solid #27ae60;border-radius:14px;">
                <tr>
                  <td style="padding:20px;">
                    <div style="font-size:16px;font-weight:700;color:#14532d;margin-bottom:10px;">Următorii pași</div>
                    <ul style="margin:0;padding:0 0 0 18px;color:#0f172a;">
                    <li style="margin-bottom: 8px;">Verificăm disponibilitatea produselor comandate.</li>
                    <li style="margin-bottom: 8px;">Vei fi notificat(ă) în cel mai scurt timp cu privire la începerea procesării.</li>
                    <li style="margin-bottom: 8px;">Poți vizualiza detaliile comenzii în contul tău de client.</li>
                    <li style="margin-bottom: 8px;">Timpul estimat de livrare: 2-5 zile lucrătoare</li>
                    <li>Poți urmări statusul comenzii în contul tău</li>
                    </ul>
                  </td>
                </tr>
              </table>
            
             <!-- Help box + footer -->
              <table role="presentation" width="100%" style="margin-top:28px;">
                <tr>
                  <td align="center" style="padding:0 8px;">
                    <div style="max-width:520px;margin:0 auto;background:#f8fafc;border:1px solid #eef2f7;border-radius:12px;padding:18px;">
                      <div style="font-size:16px;font-weight:700;color:#0f172a;margin-bottom:8px;">Ai nevoie de ajutor?</div>
                      <div style="font-size:14px;line-height:1.7;color:#334155;">
                        <div>Email: <a href="mailto:<EMAIL>" style="color:#0066B1;text-decoration:none;"><EMAIL></a></div>
                        <div>Telefon: +40 XXX XXX XXX</div>
                        <div>Program: Luni - Vineri, 09:00 - 18:00</div>
                      </div>
                    </div>
                    <div style="font-size:12px;color:#98a2b3;margin-top:14px;">
                      © ${new Date().getFullYear()} Automobile Bavaria. Toate drepturile rezervate.
                    </div>
                  </td>
                </tr>
              </table>

            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
    `,
    text: `
🚗 AUTOMOBILE BAVARIA - Confirmare Comandă

Bună ziua ${customerName},

✅ Comanda #${orderNumber} a fost confirmată cu succes!
📅 Data: ${formatDate(new Date().toISOString())}

🔧 PIESE COMANDATE:
${items.map(item => `
• ${item.name}
  OE: ${item.code}
  Cantitate: ${item.quantity} × ${formatPriceRON(item.price)} = ${formatPriceRON(item.quantity * item.price)}
`).join('')}

💰 TOTAL: ${formatPriceRON(total)}

${shippingAddress ? `
🚚 ADRESA DE LIVRARE:
${shippingAddress.street}
${shippingAddress.city}, ${shippingAddress.state} ${shippingAddress.zipCode}
${shippingAddress.country}
` : ''}

📋 URMĂTORII PAȘI:
• Comanda este în curs de procesare
• Vei primi email de confirmare a expedierii
• Timp estimat de livrare: 2-5 zile lucrătoare

📧 Contact: <EMAIL>

Mulțumim pentru încrederea acordată!
Automobile Bavaria
    `
  };
};

// Main email sending function
export const sendOrderConfirmationEmail = async (orderData : OrderDataEmail) => {
  try {
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.FROM_EMAIL) {
      throw new Error('Missing required email environment variables');
    }

    if (!isValidEmail(orderData.customerEmail)) {
      throw new Error(`Invalid customer email: ${orderData.customerEmail}`);
    }

    if (!isValidEmail(process.env.FROM_EMAIL)) {
      throw new Error(`Invalid FROM_EMAIL: ${process.env.FROM_EMAIL}`);
    }
    
    // Verify connection
    await createTransport.verify();
    
    const template = getOrderConfirmationTemplate(orderData);
    
    const mailOptions = {
      from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
      to: orderData.customerEmail,
      bcc: process.env.FROM_EMAIL, // Keep a copy for records
      subject: template.subject,
      html: template.html,
      text: template.text,
      // Add tracking headers
      headers: {
        'X-Order-ID': orderData.orderNumber,
        'X-Customer-ID': orderData.customerId,
      },
    };
    
    const result = await createTransport.sendMail(mailOptions);
    
    logger.info('Order confirmation email sent:', {
      orderId: orderData.orderNumber,
      messageId: result.messageId,
      to: orderData.customerEmail
    });
    
    return { success: true };
    
  } catch (error) {
    logger.error(`[sendOrderConfirmationEmail] Error sending email: ${error}`);
    
    // Don't throw error - email failure shouldn't break order processing
    return { success: false };
  }
};

// Service status email templates
const getServiceStatusTemplate = (serviceData: ServiceEmailData) => {
  const { serviceNumber, customerName, status, productName, productCode, orderNumber, resolution, resolutionNotes } = serviceData;

  const statusConfig: Record<string, {
    subject: string;
    title: string;
    message: string;
    color: string;
    icon: string;
    nextSteps: string[];
  }> = {
    requested: {
      subject: `Cerere Service înregistrată #${serviceNumber} - Automobile Bavaria`,
      title: 'Service Înregistrat',
      message: 'Cererea ta de service a fost înregistrată. Echipa noastră va analiza detaliile.',
      color: '#0066B1',
      icon: '',
      nextSteps: [
            'Verificăm documentația și detaliile solicitate.',
            'Un specialist va analiza cererea de garanție/service.',
            'Vei fi contactat(ă) în curând pentru confirmarea programării.'
      ]
    },
    cancelled: {
      subject: `Cerere Service anulata #${serviceNumber} - Automobile Bavaria`,
      title: 'Service Anulat',
      message: 'Cererea ta de service a fost anulata de catre tine.',
      color: '#0066B1',
      icon: '',
      nextSteps: [
        'Serviciul a fost anulat.',
        'Produsul va fi returnat în starea inițială.',
        'Pentru a discuta cu un consultant, te rugăm să ne contactezi.',
      ]
    }
  };

  const config = statusConfig[status] || statusConfig.requested;

  return {
    subject: config.subject,
    html: `
<!DOCTYPE html>
<html lang="ro">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title>Actualizare Garanție</title>
  <style>
    html,body { margin:0; padding:0; background:#f5f6f8; }
    table { border-collapse: collapse; }
    img { border:0; outline:none; text-decoration:none; -ms-interpolation-mode:bicubic; }
    a { color: inherit; text-decoration: none; }
    @media (max-width: 600px) {
      .container { width:100% !important; }
      .px { padding-left:20px !important; padding-right:20px !important; }
      .py { padding-top:24px !important; padding-bottom:24px !important; }
    }
  </style>
</head>
<body style="margin:0;padding:0;background:#f5f6f8;">
  <!-- Preheader (hidden in inbox preview) -->
  <span style="display:none!important;opacity:0;color:transparent;visibility:hidden;mso-hide:all;">
    ${config.message} — Actualizare dosar garanție #${serviceNumber}
  </span>

  <table role="presentation" width="100%" bgcolor="#f5f6f8">
    <tr>
      <td align="center" style="padding:32px 16px;">
        <table role="presentation" width="600" class="container" style="width:600px;max-width:600px;background:#ffffff;border-radius:16px;overflow:hidden;box-shadow:0 8px 30px rgba(16,24,40,0.08);">
          <!-- Header -->
          <tr>
            <td style="background:${config.color};padding:32px 40px;color:#ffffff;text-align:center;">
              <div style="font-family:Inter,'Segoe UI','Helvetica Neue',Arial,sans-serif;font-size:26px;font-weight:800;letter-spacing:0.3px;">
                AUTOMOBILE BAVARIA
              </div>
              <div style="margin-top:12px;display:inline-block;border:1px solid rgba(255,255,255,0.35);border-radius:999px;padding:8px 16px;font-family:Inter,'Segoe UI','Helvetica Neue',Arial,sans-serif;font-weight:600;font-size:13px;letter-spacing:0.3px;">
                ${config.title}
              </div>
              <div style="margin-top:12px;font-family:Inter,'Segoe UI','Helvetica Neue',Arial,sans-serif;font-size:18px;line-height:1.4;font-weight:500;opacity:0.95;">
                ${config.message}
              </div>
            </td>
          </tr>

          <!-- Body -->
          <tr>
            <td class="px py" style="padding:32px 40px;font-family:Inter,'Segoe UI','Helvetica Neue',Arial,sans-serif;color:#101828;">
              <p style="font-size:17px;line-height:1.7;margin:0 0 20px 0;">Bună ziua <strong>${customerName}</strong>,</p>

              <!-- Warranty summary -->
              <table role="presentation" width="100%" style="background:#fafbfc;border:1px solid #eef0f3;border-radius:14px;">
                <tr>
                  <td style="padding:20px 20px 12px 20px;">
                    <table role="presentation" width="100%">
                      <tr>
                        <td style="font-size:16px;font-weight:700;color:${config.color};">Dosar garanție #${serviceNumber}</td>
                        <td align="right" style="font-size:13px;color:#667085;">${formatDate(new Date().toISOString())}</td>
                      </tr>
                    </table>
                  </td>
                </tr>

                <tr>
                  <td style="padding:4px 20px 6px 20px;">
                    <div style="font-size:15px;font-weight:700;color:#101828;margin:8px 0 6px 0;">Produs în garanție</div>
                  </td>
                </tr>

                <!-- Product info -->
                <tr>
                  <td style="padding:0 20px 20px 20px;">
                    <table role="presentation" width="100%" style="background:#ffffff;border:1px solid #eef0f3;border-radius:10px;">
                      <tr>
                        <td style="padding:16px;">
                          
                          <div style="font-size:14px;color:#101828;font-weight:600;">${productName}</div>
                          <div style="margin-top:6px;">
                            <span style="display:inline-block;font-family:'Courier New',monospace;background:#f1f3f4;color:#475467;font-size:12px;padding:4px 8px;border-radius:6px;">Cod: ${productCode}</span>
                          </div>
                          <div style="font-size:13px;color:#667085;margin-top:10px;">Comandă asociată: #${orderNumber}</div>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>

              <!-- Next steps -->
              <table role="presentation" width="100%" style="margin-top:24px;background:#f0f9f2;border:1px solid #cfe9d6;border-left:4px solid #27ae60;border-radius:14px;">
                <tr>
                  <td style="padding:20px;">
                    <div style="font-size:16px;font-weight:700;color:#14532d;margin-bottom:10px;">Următorii pași</div>
                    <ul style="margin:0;padding:0 0 0 18px;color:#0f172a;">
                      ${config.nextSteps.map((step: string) => `<li style="margin-bottom:6px;font-size:14px;line-height:1.6;">${step}</li>`).join('')}
                    </ul>
                  </td>
                </tr>
              </table>

              <!-- Help box + footer -->
              <table role="presentation" width="100%" style="margin-top:28px;">
                <tr>
                  <td align="center" style="padding:0 8px;">
                    <div style="max-width:520px;margin:0 auto;background:#f8fafc;border:1px solid #eef2f7;border-radius:12px;padding:18px;">
                      <div style="font-size:16px;font-weight:700;color:#0f172a;margin-bottom:8px;">Ai nevoie de ajutor?</div>
                      <div style="font-size:14px;line-height:1.7;color:#334155;">
                        <div>Email: <a href="mailto:<EMAIL>" style="color:${config.color};text-decoration:none;"><EMAIL></a></div>
                        <div>Telefon: +40 XXX XXX XXX</div>
                        <div>Program: Luni - Vineri, 09:00 - 18:00</div>
                      </div>
                    </div>
                    <div style="font-size:12px;color:#98a2b3;margin-top:14px;">
                      © ${new Date().getFullYear()} Automobile Bavaria. Toate drepturile rezervate.
                    </div>
                  </td>
                </tr>
              </table>

            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
`,
    text: `
🚗 AUTOMOBILE BAVARIA - ${config.title}

Bună ziua ${customerName},

${config.icon} ${config.message}

🔧 DETALII SERVICE:
Service: #${serviceNumber}
📅 Data: ${formatDate(new Date().toISOString())}

🔧 PRODUS:
${productName}
Cod: ${productCode}
📦 Comandă: #${orderNumber}

${resolution ? `
📋 REZOLUȚIE: ${resolution}
${resolutionNotes ? resolutionNotes : ''}
` : ''}

📋 URMĂTORII PAȘI:
${config.nextSteps.map((step: string) => `• ${step}`).join('\n')}

📧 Contact: <EMAIL>

Mulțumim pentru încrederea acordată!
Automobile Bavaria
    `
  };
};

// Main service email sending function
export const sendServiceStatusEmail = async (serviceData: ServiceEmailData) => {
  try {
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.FROM_EMAIL) {
      throw new Error('Missing required email environment variables');
    }

    if (!isValidEmail(serviceData.customerEmail)) {
      throw new Error(`Invalid customer email: ${serviceData.customerEmail}`);
    }

    if (!isValidEmail(process.env.FROM_EMAIL)) {
      throw new Error(`Invalid FROM_EMAIL: ${process.env.FROM_EMAIL}`);
    }

    // Verify connection
    await createTransport.verify();

    const template = getServiceStatusTemplate(serviceData);

    const mailOptions = {
      from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
      to: serviceData.customerEmail,
      bcc: process.env.FROM_EMAIL, // Keep a copy for records
      subject: template.subject,
      html: template.html,
      text: template.text,
      // Add tracking headers
      headers: {
        'X-Service-Number': serviceData.serviceNumber,
        'X-Service-Status': serviceData.status,
        'X-Order-Number': serviceData.orderNumber,
      },
    };

    const result = await createTransport.sendMail(mailOptions);

    logger.info('Service status email sent:', {
      serviceNumber: serviceData.serviceNumber,
      status: serviceData.status,
      messageId: result.messageId,
      to: serviceData.customerEmail
    });

    return { success: true };

  } catch (error) {
    logger.error(`[sendServiceStatusEmail] Error sending email: ${error}`);

    // Don't throw error - email failure shouldn't break service processing
    return { success: false };
  }
};

// Return confirmation email template
const getReturnConfirmationTemplate = (returnData: {
  returnNumber: string;
  customerName: string;
  orderNumber: string;
  items: Array<{
    name: string;
    code: string;
    quantity: number;
    reason: string;
  }>;
}) => {
  const { returnNumber, customerName, orderNumber, items } = returnData;

  return {
    subject: `📦 Returnare #${returnNumber} confirmată - Automobile Bavaria`,
    html: `
      <!DOCTYPE html>
      <html lang="ro">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Returnare Confirmată</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
            .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
            .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }
            .content { padding: 30px; }
            .status-badge { display: inline-block; background-color: #10b981; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: bold; margin: 10px 0; }
            .item-card { border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin: 10px 0; background-color: #f9fafb; }
            .footer { background-color: #1f2937; color: white; padding: 30px; text-align: center; }
            .contact-info p { margin: 5px 0; }
            .contact-email { color: #60a5fa; text-decoration: none; }
            .next-steps { background-color: #eff6ff; border-left: 4px solid #3b82f6; padding: 15px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🚗 AUTOMOBILE BAVARIA</h1>
              <h2>Returnare Confirmată</h2>
              <div class="status-badge">✅ CONFIRMATĂ</div>
            </div>

            <div class="content">
              <p>Bună ziua <strong>${customerName}</strong>,</p>

              <p>Returnarea <strong>#${returnNumber}</strong> a fost confirmată cu succes!</p>

              <div class="next-steps">
                <h3>📋 Detalii Returnare:</h3>
                <p><strong>Număr returnare:</strong> #${returnNumber}</p>
                <p><strong>Comandă originală:</strong> #${orderNumber}</p>
                <p><strong>Data:</strong> ${formatDate(new Date().toISOString())}</p>
              </div>

              <h3>📦 Produse pentru returnare:</h3>
              ${items.map(item => `
                <div class="item-card">
                  <h4>${item.name}</h4>
                  <p><strong>Cod:</strong> ${item.code}</p>
                  <p><strong>Cantitate:</strong> ${item.quantity}</p>
                  <p><strong>Motiv:</strong> ${item.reason}</p>
                </div>
              `).join('')}

              <div class="next-steps">
                <h3>📋 Următorii pași:</h3>
                <ul>
                  <li>Vei fi contactat în maxim 24 de ore pentru detalii</li>
                  <li>Pregătește produsele pentru ridicare/returnare</li>
                  <li>Păstrează ambalajul original dacă este posibil</li>
                  <li>Poți urmări statusul în secțiunea "Returnări"</li>
                </ul>
              </div>
            </div>

            <div class="footer">
              <div style="max-width: 500px; margin: 0 auto;">
                <h3 style="margin-bottom: 15px;">Ai nevoie de ajutor?</h3>
                <div class="contact-info">
                  <p>📧 Email: <a href="mailto:<EMAIL>" class="contact-email"><EMAIL></a></p>
                  <p>📞 Telefon: +40 XXX XXX XXX</p>
                  <p>🕒 Program: Luni - Vineri, 09:00 - 18:00</p>
                </div>

                <p style="margin-top: 20px; opacity: 0.8; font-size: 14px;">
                  © 2025 Automobile Bavaria. Toate drepturile rezervate.
                </p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
🚗 AUTOMOBILE BAVARIA - Returnare Confirmată

Bună ziua ${customerName},

📦 Returnarea #${returnNumber} a fost confirmată cu succes!
📅 Data: ${formatDate(new Date().toISOString())}

📋 DETALII RETURNARE:
Returnare: #${returnNumber}
Comandă originală: #${orderNumber}

📦 PRODUSE PENTRU RETURNARE:
${items.map(item => `
• ${item.name}
  Cod: ${item.code}
  Cantitate: ${item.quantity}
  Motiv: ${item.reason}
`).join('')}

📋 URMĂTORII PAȘI:
• Vei fi contactat în maxim 24 de ore pentru detalii
• Pregătește produsele pentru ridicare/returnare
• Păstrează ambalajul original dacă este posibil
• Poți urmări statusul în secțiunea "Returnări"

📧 Contact: <EMAIL>

Mulțumim pentru încrederea acordată!
Automobile Bavaria
    `
  };
};

// Return email sending function
export const sendReturnConfirmationEmail = async (returnData: {
  returnNumber: string;
  customerName: string;
  customerEmail: string;
  orderNumber: string;
  items: Array<{
    name: string;
    code: string;
    quantity: number;
    reason: string;
  }>;
}) => {
  try {
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.FROM_EMAIL) {
      throw new Error('Missing required email environment variables');
    }

    if (!isValidEmail(returnData.customerEmail)) {
      throw new Error(`Invalid customer email: ${returnData.customerEmail}`);
    }

    if (!isValidEmail(process.env.FROM_EMAIL)) {
      throw new Error(`Invalid FROM_EMAIL: ${process.env.FROM_EMAIL}`);
    }

    // Verify connection
    await createTransport.verify();

    const template = getReturnConfirmationTemplate(returnData);

    const mailOptions = {
      from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
      to: returnData.customerEmail,
      bcc: process.env.FROM_EMAIL, // Keep a copy for records
      subject: template.subject,
      html: template.html,
      text: template.text,
      // Add tracking headers
      headers: {
        'X-Return-Number': returnData.returnNumber,
        'X-Order-Number': returnData.orderNumber,
      },
    };

    const result = await createTransport.sendMail(mailOptions);

    logger.info('Return confirmation email sent:', {
      returnNumber: returnData.returnNumber,
      messageId: result.messageId,
      to: returnData.customerEmail
    });

    return { success: true };

  } catch (error) {
    logger.error(`[sendReturnConfirmationEmail] Error sending email: ${error}`);

    // Don't throw error - email failure shouldn't break return processing
    return { success: false };
  }
};
