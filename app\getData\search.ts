"server-only"

import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { toSafeNumber } from "@/lib/utils";
import { AttributeData, BrandData, CategoryData, ClassData, 
  SearchFilters, SearchPageData, SearchSuggestionsResult } from "@/types/search";
import { ProductCardInterface } from "@/types/product";
import { getPricesFor4thBatch } from "@/lib/mssql/query";
import { getCurrentDbUser } from "@/lib/auth";
import { Decimal } from "@/generated/prisma/runtime/library";
import { Prisma } from "@/generated/prisma";

// Get search suggestions (max 5 products + first relevant category)
export async function getSearchSuggestions(query: string): Promise<SearchSuggestionsResult> {
  const user = await getCurrentDbUser()
  if (!user) {
    logger.error(`[getSearchSuggestions] No user authenticated`);
    return { products: [], firstCategory: null }
  }
  const has4th = user.role.includes("fourLvlAdminAB") || user.role.includes("fourLvlInregistratAB")
  const userAM = user.userAM || ""

  if (!query || query.length < 3) {
    return { products: [], firstCategory: null }
  }

  try {
    // Search for products
    const products = await withRetry(() => prisma.product.findMany({
      where: {
        OR: [
          { Material_Number: { contains: query } },
          { Description_Local: { contains: query } }
        ],
        isActive: true
      },
      take: 5,
      select: {
        Material_Number: true,
        Description_Local: true,
        ImageUrl: true,
        FinalPrice: true,
        PretAM: true,
        HasDiscount: true,
        activeDiscountType: true,
        activeDiscountValue: true,
        discountPercentage: true,
        categoryLevel3: {
          where: {
            afisat: true
          },
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      },
      orderBy: [
        { HasDiscount: 'desc' }, // Prioritize discounted products
        { FinalPrice: 'asc' }
      ]
    }))

    // Find first relevant category from the products found
    const firstCategory = products.length > 0 && products[0].categoryLevel3
      ? {
          id: products[0].categoryLevel3.id,
          name: products[0].categoryLevel3.name,
          slug: products[0].categoryLevel3.slug
        }
      : null

    // Remove categoryLevel3 from products for cleaner response
    //const cleanProducts = products.map(({ categoryLevel3, ...product }) => product)
    const cleanProducts = products.map(({ ...product }) => product)

    //check if the user is 4th level user and if so, get the prices for the products
    if (has4th) {
    const productSkus = cleanProducts.map((p) => p.Material_Number)
    const price4Batch = await getPricesFor4thBatch(productSkus, userAM || "")
    const price4Map = new Map(price4Batch.map((p) => [p.itemno, p.pret]))
cleanProducts.forEach((product) => {
  const price = price4Map.get(product.Material_Number);
  if (price !== undefined) {
    product.FinalPrice = new Decimal(price);
  }
  // If price is undefined, FinalPrice remains unchanged
})
  }

    //convert cleanProducts to SearchSuggestionsResult interface, example :Decimal to number
    const searchSuggestionsResult = cleanProducts.map((product) => ({
      Material_Number: product.Material_Number,
      Description_Local: product.Description_Local,
      ImageUrl: product.ImageUrl,
      FinalPrice: toSafeNumber(product.FinalPrice) ,
      PretAM: toSafeNumber(product.PretAM),
      HasDiscount: product.HasDiscount,
      activeDiscountType: product.activeDiscountType,
      activeDiscountValue:  toSafeNumber(product.activeDiscountValue),
      discountPercentage:  toSafeNumber(product.discountPercentage)
    }))

    return {
      products: searchSuggestionsResult,
      firstCategory
    }

  } catch (error) {
    logger.error('Error fetching search suggestions:', error)
    return { products: [], firstCategory: null }
  }
}

// Get search page data with filters
// export async function getSearchPageData(filters: SearchFilters): Promise<SearchPageData> {
//   const {
//     query = '',
//     category3,
//     category3Id,
//     brands = [],
//     classes = [],
//     attributes = {},
//     minPrice,
//     maxPrice,
//     hasDiscount,
//     page = 1,
//     sort = 'relevance'
//   } = filters

//   const pageSize = 24
//   const skip = (page - 1) * pageSize

//   // Use category3Id if provided, otherwise convert category3 name to ID for filtering
//   let categoryId: string | undefined = undefined
//   if (category3Id) {
//     categoryId = category3Id
//   } else if (category3) {
//     const categoryRecord = await withRetry(() => prisma.categoryLevel3.findFirst({
//       where: { name: category3 },
//       select: { id: true }
//     }))
//     categoryId = categoryRecord?.id
//   }

//   // Convert brand names to IDs for filtering
//   let brandIds: string[] = []
//   if (brands.length > 0) {
//     const brandRecords = await withRetry(() => prisma.brand.findMany({
//       where: { name: { in: brands } },
//       select: { id: true }
//     }))
//     brandIds = brandRecords.map(b => b.id)
//   }

//   // Convert class names to original class IDs for filtering
//   let classIds: string[] = []
//   if (classes.length > 0) {
//     const classRecords = await withRetry(() => prisma.productClass.findMany({
//       where: {
//         vehicleModels: {
//           some: {
//             vehicleModel: {
//               name: { in: classes }
//             }
//           }
//         }
//       },
//       select: { id: true }
//     }))
//     classIds = classRecords.map(c => c.id)
//   }

//   try {
//     // Build where clause
//     const whereClause: any = {
//       isActive: true,
//       AND: []
//     }

//     // Search query
//     if (query && query.length >= 3) {
//       whereClause.AND.push({
//         OR: [
//           { Material_Number: { contains: query.toLowerCase() } },
//           { Description_Local: { contains: query.toLowerCase() } }
//         ]
//       })
//     }

//     // Category filter
//     if (categoryId) {
//       whereClause.AND.push({
//         categoryLevel3Id: categoryId
//       })
//     }

//     // Brand filter (through ProductClass)
//     if (brandIds.length > 0) {
//       whereClause.AND.push({
//         productClass: {
//           brand: {
//             id: { in: brandIds }
//           }
//         }
//       })
//     }

//     // Class filter
//     if (classIds.length > 0) {
//       whereClause.AND.push({
//         classId: { in: classIds }
//       })
//     }

//     // Attributes filter
//     if (Object.keys(attributes).length > 0) {
//       Object.entries(attributes).forEach(([key, values]) => {
//         if (values.length > 0) {
//           whereClause.AND.push({
//             attributes: {
//               some: {
//                 key: key,
//                 value: { in: values }
//               }
//             }
//           })
//         }
//       })
//     }

//     // Price range filter
//     if (minPrice !== undefined || maxPrice !== undefined) {
//       const priceFilter: any = {}
//       if (minPrice !== undefined) priceFilter.gte = minPrice
//       if (maxPrice !== undefined) priceFilter.lte = maxPrice
//       whereClause.AND.push({
//         FinalPrice: priceFilter
//       })
//     }

//     // Discount filter
//     if (hasDiscount) {
//       whereClause.AND.push({
//         HasDiscount: true
//       })
//     }

//     // Build order by clause
//     let orderBy: any = []
//     switch (sort) {
//       case 'price_asc':
//         orderBy = [{ FinalPrice: 'asc' }]
//         break
//       case 'price_desc':
//         orderBy = [{ FinalPrice: 'desc' }]
//         break
//       case 'discount_desc':
//         orderBy = [{ discountPercentage: 'desc' }, { FinalPrice: 'asc' }]
//         break
//       case 'name_asc':
//         orderBy = [{ Description_Local: 'asc' }]
//         break
//       case 'relevance':
//       default:
//         // Relevance sorting: prioritize discounted products, then by creation date (newer first)
//         orderBy = [
//           { HasDiscount: 'desc' },
//           { createdAt: 'desc' },
//           { Description_Local: 'asc' }
//         ]
//         break
//     }

//     // Execute queries in parallel
//     const [products, totalCount, categories, brands_data, classes_data, priceRange, attributes_data] = await Promise.all([
//       // Products
//       withRetry(() => prisma.product.findMany({
//         where: whereClause,
//         select: {
//           id: true,
//           Material_Number: true,
//           Description_Local: true,
//           ImageUrl: true,
//           FinalPrice: true,
//           PretAM: true,
//           HasDiscount: true,
//           activeDiscountType: true,
//           activeDiscountValue: true,
//           discountPercentage: true,
//           categoryLevel3: {
//             select: {
//               id: true,
//               name: true,
//               slug: true
//             }
//           },
//           productClass: {
//             select: {
//               id: true,
//               classCode: true,
//               brand: {
//                 select: {
//                   id: true,
//                   name: true
//                 }
//               },
//               vehicleModels: {
//                 select: {
//                   vehicleModel: {
//                     select: {
//                       name: true
//                     }
//                   }
//                 }
//               }
//             }
//           }
//         },
//         orderBy,
//         skip,
//         take: pageSize
//       })),

//       // Total count
//       withRetry(() => prisma.product.count({
//         where: whereClause
//       })),

//       // Categories with counts
//       withRetry(() => prisma.categoryLevel3.findMany({
//         where: {
//           products: {
//             some: {
//               isActive: true,
//               ...(query && query.length >= 3 ? {
//                 OR: [
//                   { Material_Number: { contains: query.toLowerCase() } },
//                   { Description_Local: { contains: query.toLowerCase() } }
//                 ]
//               } : {})
//             }
//           }
//         },
//         select: {
//           id: true,
//           name: true,
//           slug: true,
//           _count: {
//             select: {
//               products: {
//                 where: {
//                   isActive: true,
//                   ...(query && query.length >= 3 ? {
//                     OR: [
//                       { Material_Number: { contains: query.toLowerCase() } },
//                       { Description_Local: { contains: query.toLowerCase() } }
//                     ]
//                   } : {})
//                 }
//               }
//             }
//           }
//         },
//         orderBy: {
//           name: 'asc'
//         }
//       })),

//       // Brands with counts
//       withRetry(() => prisma.brand.findMany({
//         where: {
//           productClasses: {
//             some: {
//               products: {
//                 some: {
//                   isActive: true,
//                   ...(categoryId ? { categoryLevel3Id: categoryId } : {}),
//                   ...(query && query.length >= 3 ? {
//                     OR: [
//                       { Material_Number: { contains: query.toLowerCase() } },
//                       { Description_Local: { contains: query.toLowerCase() } }
//                     ]
//                   } : {})
//                 }
//               }
//             }
//           }
//         },
//         select: {
//           id: true,
//           name: true,
//           productClasses: {
//             select: {
//               _count: {
//                 select: {
//                   products: {
//                     where: {
//                       isActive: true,
//                       ...(categoryId ? { categoryLevel3Id: categoryId } : {}),
//                       ...(query && query.length >= 3 ? {
//                         OR: [
//                           { Material_Number: { contains: query.toLowerCase() } },
//                           { Description_Local: { contains: query.toLowerCase() } }
//                         ]
//                       } : {})
//                     }
//                   }
//                 }
//               }
//             }
//           }
//         },
//         orderBy: {
//           name: 'asc'
//         }
//       })),

//       // Classes with counts
//       withRetry(() => prisma.productClass.findMany({
//         where: {
//           products: {
//             some: {
//               isActive: true,
//               ...(categoryId ? { categoryLevel3Id: categoryId } : {}),
//               ...(query && query.length >= 3 ? {
//                 OR: [
//                   { Material_Number: { contains: query.toLowerCase() } },
//                   { Description_Local: { contains: query.toLowerCase() } }
//                 ]
//               } : {})
//             }
//           }
//         },
//         select: {
//           id: true,
//           classCode: true,
//           brand: {
//             select: {
//               id: true,
//               name: true
//             }
//           },
//           vehicleModels: {
//             select: {
//               vehicleModel: {
//                 select: {
//                   name: true
//                 }
//               }
//             }
//           },
//           _count: {
//             select: {
//               products: {
//                 where: {
//                   isActive: true,
//                   ...(categoryId ? { categoryLevel3Id: categoryId } : {}),
//                   ...(query && query.length >= 3 ? {
//                     OR: [
//                       { Material_Number: { contains: query.toLowerCase() } },
//                       { Description_Local: { contains: query.toLowerCase() } }
//                     ]
//                   } : {})
//                 }
//               }
//             }
//           }
//         },
//         orderBy: {
//           classCode: 'asc'
//         }
//       })),

//       // Price range - based on current filters (excluding price filters)
//       withRetry(() => prisma.product.aggregate({
//         where: {
//           isActive: true,
//           FinalPrice: { not: null },
//           ...(categoryId ? { categoryLevel3Id: categoryId } : {}),
//           ...(brandIds.length > 0 ? {
//             productClass: {
//               brand: {
//                 id: { in: brandIds }
//               }
//             }
//           } : {}),
//           ...(classIds.length > 0 ? { classId: { in: classIds } } : {}),
//           ...(hasDiscount ? { HasDiscount: true } : {}),
//           ...(query && query.length >= 3 ? {
//             OR: [
//               { Material_Number: { contains: query.toLowerCase() } },
//               { Description_Local: { contains: query.toLowerCase() } }
//             ]
//           } : {})
//         },
//         _min: {
//           FinalPrice: true
//         },
//         _max: {
//           FinalPrice: true
//         }
//       })),

//       // Attributes - get all unique key-value pairs with counts
//       withRetry(() => prisma.productAttribute.groupBy({
//         by: ['key', 'value'],
//         where: {
//           product: {
//             isActive: true,
//             ...(categoryId ? { categoryLevel3Id: categoryId } : {}),
//             ...(brandIds.length > 0 ? {
//               productClass: {
//                 brand: {
//                   id: { in: brandIds }
//                 }
//               }
//             } : {}),
//             ...(classIds.length > 0 ? { classId: { in: classIds } } : {}),
//             ...(hasDiscount ? { HasDiscount: true } : {}),
//             ...(query && query.length >= 3 ? {
//               OR: [
//                 { Material_Number: { contains: query.toLowerCase() } },
//                 { Description_Local: { contains: query.toLowerCase() } }
//               ]
//             } : {})
//           },
//           key: { not: null },
//           value: { not: null }
//         },
//         _count: {
//           Material_Number: true
//         },
//         orderBy: [
//           { key: 'asc' },
//           { value: 'asc' }
//         ]
//       }))
//     ])

//     // Process products
//     const processedProducts: ProductCardInterface[] = products.map(product => ({
//       id: product.id,
//       Material_Number: product.Material_Number,
//       Description_Local: product.Description_Local,
//       ImageUrl: product.ImageUrl,
//       FinalPrice: toSafeNumber(product.FinalPrice),
//       PretAM: toSafeNumber(product.PretAM),
//       HasDiscount: product.HasDiscount,
//       activeDiscountType: product.activeDiscountType,
//       activeDiscountValue: toSafeNumber(product.activeDiscountValue),
//       discountPercentage: toSafeNumber(product.discountPercentage),
//       categoryLevel3: product.categoryLevel3,
//       productClass: product.productClass
//     }))

//     // Process categories
//     const processedCategories = categories.map(cat => ({
//       id: cat.id,
//       name: cat.name,
//       slug: cat.slug,
//       productCount: cat._count.products
//     }))

//     // Process brands
//     const processedBrands = brands_data.map(brand => ({
//       id: brand.id,
//       name: brand.name,
//       productCount: brand.productClasses.reduce((total, pc) => total + pc._count.products, 0)
//     }))

//     // Process classes - create individual entries for each VehicleModel
//     const processedClasses = classes_data.flatMap(cls => {
//       // Get all vehicle model names for this class
//       const vehicleModelNames = cls.vehicleModels.map(vm => vm.vehicleModel.name)

//       if (vehicleModelNames.length > 0) {
//         // Create separate entries for each vehicle model
//         return vehicleModelNames.map(modelName => ({
//           id: `${cls.id}-${modelName}`, // Unique ID combining class ID and model name
//           name: modelName,
//           productCount: cls._count.products,
//           brandId: cls.brand.id, // Add brand ID for filtering
//           originalClassId: cls.id // Keep original class ID for filtering
//         }))
//       } else {
//         // Fallback to brand-classCode if no models
//         return [{
//           id: cls.id,
//           name: `${cls.brand.name} - ${cls.classCode}`,
//           productCount: cls._count.products,
//           brandId: cls.brand.id,
//           originalClassId: cls.id
//         }]
//       }
//     })

//     // Calculate pagination
//     const totalPages = Math.ceil(totalCount / pageSize)

//     // Process attributes data
//     const processedAttributes: Array<{
//       key: string
//       values: Array<{
//         value: string
//         productCount: number
//       }>
//     }> = []

//     // Group attributes by key
//     const attributesByKey = new Map<string, Array<{ value: string; productCount: number }>>()

//     attributes_data.forEach(attr => {
//       if (!attr.key || !attr.value) return

//       if (!attributesByKey.has(attr.key)) {
//         attributesByKey.set(attr.key, [])
//       }

//       attributesByKey.get(attr.key)!.push({
//         value: attr.value,
//         productCount: attr._count.Material_Number
//       })
//     })

//     // Convert to final format
//     attributesByKey.forEach((values, key) => {
//       processedAttributes.push({
//         key,
//         values: values.sort((a, b) => b.productCount - a.productCount) // Sort by count desc
//       })
//     })

//     // Sort attributes by key name
//     processedAttributes.sort((a, b) => a.key.localeCompare(b.key))

//     // Get selected category info if categoryId is set
//     let selectedCategory: { id: string; name: string } | undefined
//     if (categoryId) {
//       const categoryInfo = await withRetry(() => prisma.categoryLevel3.findUnique({
//         where: { id: categoryId },
//         select: { id: true, name: true }
//       }))
//       if (categoryInfo) {
//         selectedCategory = categoryInfo
//       }
//     }

//     return {
//       products: processedProducts,
//       categories: processedCategories,
//       brands: processedBrands,
//       classes: processedClasses,
//       attributes: processedAttributes,
//       priceRange: {
//         min: toSafeNumber(priceRange._min.FinalPrice) || 0,
//         max: toSafeNumber(priceRange._max.FinalPrice) || 1000
//       },
//       pagination: {
//         total: totalCount,
//         pages: totalPages,
//         currentPage: page,
//         hasNext: page < totalPages,
//         hasPrev: page > 1
//       },
//       appliedFilters: filters,
//       selectedCategory
//     }

//   } catch (error) {
//     logger.error('Error fetching search page data:', error)

//     // Return empty results on error
//     return {
//       products: [],
//       categories: [],
//       brands: [],
//       classes: [],
//       attributes: [],
//       priceRange: { min: 0, max: 1000 },
//       pagination: {
//         total: 0,
//         pages: 0,
//         currentPage: 1,
//         hasNext: false,
//         hasPrev: false
//       },
//       appliedFilters: filters,
//       selectedCategory: undefined
//     }
//   }
// }

// Get category page data with filters (dedicated function for category routes)
export async function getCategoriesPageData(filters: SearchFilters): Promise<SearchPageData> {
  const {
    category2,
    category3,
    category3Id,
    brands = [],
    classes = [],
    attributes = {},
    minPrice,
    maxPrice,
    hasDiscount,
    page = 1,
    sort = 'relevance'
  } = filters

  // Get user authentication and role information
  const user = await getCurrentDbUser()
  if (!user) {
    logger.error(`[getCategoriesPageData] No user authenticated`);
    return {
      products: [],
      categories: [],
      brands: [],
      classes: [],
      attributes: [],
      priceRange: { min: 0, max: 1000 },
      pagination: {
        total: 0,
        pages: 0,
        currentPage: 1,
        hasNext: false,
        hasPrev: false
      },
      appliedFilters: filters,
      selectedCategory: undefined,
      has4th: false
    }
  }
  const has4th = user.role.includes("fourLvlAdminAB") || user.role.includes("fourLvlInregistratAB")

  const pageSize = 24
  const skip = (page - 1) * pageSize

  // Find the category2 by slug and get all its category3 IDs
  let category2Ids: string[] = []
  let category2Record = null
  let specificCategory3Id: string | undefined = undefined

  if (category2) {
    // Use slug-based lookup for category2
    category2Record = await withRetry(() => prisma.categoryLevel2.findFirst({
      where: { 
        slug: category2,
        afisat: true
      },
      select: {
        id: true,
        name: true,
        nameRO: true,
        slug: true,
        level3Categories: {
          where: {
            afisat: true,
            isActive: true,
            deletedAt: null
          },
          select: { id: true }
        }
      }
    }))

    if (category2Record) {
      category2Ids = category2Record.level3Categories.map(cat => cat.id)
    }
  }

  // If category3Id is provided, use it directly; otherwise resolve category3 by slug
  if (category3Id) {
    specificCategory3Id = category3Id
  } else if (category3 && category2) {
    // Use slug-based lookup for category3 within the specific category2
    const category3Record = await withRetry(() => prisma.categoryLevel3.findFirst({
      where: {
        slug: category3,
        level2: {
          slug: category2,
          afisat: true
        },
        afisat: true,
        isActive: true,
        deletedAt: null
      },
      select: { id: true }
    }))
    if (category3Record) {
      specificCategory3Id = category3Record.id
    }
  }

  // If no category2 found or no category3s under it, return empty results
  if (!category2Record || category2Ids.length === 0) {
    return {
      products: [],
      categories: [],
      brands: [],
      classes: [],
      attributes: [],
      priceRange: { min: 0, max: 1000 },
      pagination: {
        total: 0,
        pages: 0,
        currentPage: 1,
        hasNext: false,
        hasPrev: false
      },
      appliedFilters: filters,
      selectedCategory: undefined,
      has4th
    }
  }

  // Convert brand names to IDs for filtering
  let brandIds: string[] = []
  if (brands.length > 0) {
    const brandRecords = await withRetry(() => prisma.brand.findMany({
      where: { 
        name: { in: brands },
        afisat: true
      },
      select: { id: true }
    }))
    brandIds = brandRecords.map(b => b.id)
  }console.log("brandIds", brandIds)

  // Convert class names to original class IDs for filtering
  let classIds: string[] = []
  if (classes.length > 0) {
    const classRecords = await withRetry(() => prisma.productClass.findMany({
      where: {
        vehicleModels: {
          some: {
            vehicleModel: {
              name: { in: classes },
              afisat: true
            }
          }
        }
      },
      select: { id: true }
    }))
    classIds = classRecords.map(c => c.id)
  }
  console.log("classIds", classIds)

  try {
    // Build where clause for products in this category2 (or specific category3 if selected)
    const whereClause: Prisma.ProductWhereInput = {
      isActive: true,
      categoryLevel3: {
        afisat: true
      },
      productClass: {
        brand: {
          afisat: true
        }
      },
      categoryLevel3Id: specificCategory3Id ? specificCategory3Id : { in: category2Ids },
      AND: []
    }

    // Brand filter (through ProductClass)
    if (brandIds.length > 0) {
      (whereClause.AND as Prisma.ProductWhereInput[]).push({
        productClass: {
          brand: {
            afisat: true,
            id: { in: brandIds }
          }
        }
      })
    }

    // Class filter
    if (classIds.length > 0) {
      (whereClause.AND as Prisma.ProductWhereInput[]).push({
        classId: { in: classIds }
      })
    }

    // Attributes filter
    if (Object.keys(attributes).length > 0) {
      Object.entries(attributes).forEach(([key, values]) => {
        if (values.length > 0) {
          (whereClause.AND as Prisma.ProductWhereInput[]).push({
            attributes: {
              some: {
                key: key,
                value: { in: values }
              }
            }
          })
        }
      })
    }

    // Price filter
    if (minPrice !== undefined || maxPrice !== undefined) {
      const priceFilter: Prisma.DecimalFilter = {};
      if (minPrice !== undefined) priceFilter.gte = minPrice;
      if (maxPrice !== undefined) priceFilter.lte = maxPrice;
      (whereClause.AND as Prisma.ProductWhereInput[]).push({
        FinalPrice: priceFilter
      })
    }

    // Discount filter
    if (hasDiscount) {
      (whereClause.AND as Prisma.ProductWhereInput[]).push({
        HasDiscount: true
      })
    }

    // Sorting
    let orderBy: Prisma.ProductOrderByWithRelationInput | Prisma.ProductOrderByWithRelationInput[] = [
      { HasDiscount: 'desc' }, // Prioritize discounted products first
      { Description_Local: 'asc' } // Then by name for relevance
    ]

    switch (sort) {
      case 'price_asc':
        orderBy = { FinalPrice: 'asc' }
        break
      case 'price_desc':
        orderBy = { FinalPrice: 'desc' }
        break
      case 'discount_desc':
        orderBy = { discountPercentage: 'desc' }
        break
      case 'name_asc':
        orderBy = { Description_Local: 'asc' }
        break
      case 'relevance':
      default:
        orderBy = [
          { HasDiscount: 'desc' }, // Prioritize discounted products
          { Description_Local: 'asc' } // Then by name for relevance
        ]
        break
    }

    // Execute queries in parallel
    const [products, totalCount, categories, brands_data, classes_data, priceRange, attributes_data] = await Promise.all([
      // Products
      withRetry(() => prisma.product.findMany({
        where: whereClause,
        select: {
          id: true,
          Material_Number: true,
          Description_Local: true,
          ImageUrl: true,
          FinalPrice: true,
          PretAM: true,
          HasDiscount: true,
          activeDiscountType: true,
          activeDiscountValue: true,
          discountPercentage: true,
          categoryLevel3: {
            where: {
              afisat: true
            },
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          productClass: {
            where: {
              brand: {
                afisat: true
              }
            },
            select: {
              id: true,
              classCode: true,
              brand: {
                select: {
                  afisat: true,
                  id: true,
                  name: true
                }
              },
              vehicleModels: {
                select: {
                  vehicleModel: {
                    select: {
                      name: true
                    }
                  }
                },
                where: {
                  vehicleModel: {
                    afisat: true
                  }
                }
              }
            }
          }
        },
        orderBy,
        skip,
        take: pageSize
      })),

      // Total count
      withRetry(() => prisma.product.count({
        where: whereClause
      })),

      // Categories - WITHOUT brand/class filters
      withRetry(() => prisma.categoryLevel3.findMany({
        where: {
          id: { in: category2Ids },
          products: {
            some: {
              isActive: true
            }
          },
          afisat: true
        },
        select: {
          id: true,
          name: true,
          nameRO: true,
          slug: true,
          _count: {
            select: {
              products: {
                where: {
                  isActive: true,
                  // Don't apply category3 filter here - show counts for all category3s
                  ...(minPrice !== undefined || maxPrice !== undefined ? {
                    FinalPrice: {
                      ...(minPrice !== undefined ? { gte: minPrice } : {}),
                      ...(maxPrice !== undefined ? { lte: maxPrice } : {})
                    }
                  } : {}),
                  ...(hasDiscount ? { HasDiscount: true } : {})
                }
              },
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      })),

      // Brands with counts - only brands that have products in this category2/category3
      withRetry(() => prisma.brand.findMany({
        where: {
          productClasses: {
            some: {
              products: {
                some: {
                  isActive: true,
                  categoryLevel3Id: specificCategory3Id ? specificCategory3Id : { in: category2Ids }
                }
              }
            }
          },
          afisat: true
        },
        select: {
          id: true,
          name: true,
          productClasses: {
            select: {
              _count: {
                select: {
                  products: {
                    where: {
                      isActive: true,
                      categoryLevel3Id: specificCategory3Id ? specificCategory3Id : { in: category2Ids },
                      ...(classIds.length > 0 ? { classId: { in: classIds } } : {}),
                      ...(minPrice !== undefined || maxPrice !== undefined ? {
                        FinalPrice: {
                          ...(minPrice !== undefined ? { gte: minPrice } : {}),
                          ...(maxPrice !== undefined ? { lte: maxPrice } : {})
                        }
                      } : {}),
                      ...(hasDiscount ? { HasDiscount: true } : {})
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      })),

      // Classes with counts - only classes that have products in this category2/category3
      withRetry(() => prisma.productClass.findMany({
        where: {
          products: {
            some: {
              isActive: true,
              categoryLevel3Id: specificCategory3Id ? specificCategory3Id : { in: category2Ids }
            }
          },
          brand: {
            afisat: true
          }
        },
        select: {
          id: true,
          classCode: true,
          brand: {
            select: {
              afisat: true,
              id: true,
              name: true
            }
          },
          vehicleModels: {
            where: {
              vehicleModel: {
                afisat: true
              }
            },
            select: {
              vehicleModel: {
                select: {
                  name: true
                }
              }
            }
          },
          _count: {
            select: {
              products: {
                where: {
                  isActive: true,
                  categoryLevel3Id: specificCategory3Id ? specificCategory3Id : { in: category2Ids },
                  ...(brandIds.length > 0 ? {
                    productClass: {
                      brand: {
                        afisat: true,
                        id: { in: brandIds }
                      }
                    }
                  } : {}),
                  ...(minPrice !== undefined || maxPrice !== undefined ? {
                    FinalPrice: {
                      ...(minPrice !== undefined ? { gte: minPrice } : {}),
                      ...(maxPrice !== undefined ? { lte: maxPrice } : {})
                    }
                  } : {}),
                  ...(hasDiscount ? { HasDiscount: true } : {})
                }
              }
            }
          }
        },
        orderBy: {
          classCode: 'asc'
        }
      })),

      // Price range - based on products in this category2/category3 and current filters (excluding price filters)
      withRetry(() => prisma.product.aggregate({
        where: {
          isActive: true,
          FinalPrice: { not: null },
          categoryLevel3Id: specificCategory3Id ? specificCategory3Id : { in: category2Ids },
          ...(brandIds.length > 0 ? {
            productClass: {
              brand: {
                afisat: true,
                id: { in: brandIds }
              }
            }
          } : {}),
          ...(classIds.length > 0 ? { classId: { in: classIds } } : {}),
          ...(hasDiscount ? { HasDiscount: true } : {})
        },
        _min: {
          FinalPrice: true
        },
        _max: {
          FinalPrice: true
        }
      })),

      // Attributes - get all unique key-value pairs with counts for this category
      withRetry(() => prisma.productAttribute.groupBy({
        by: ['key', 'value'],
        where: {
          product: {
            isActive: true,
            categoryLevel3Id: specificCategory3Id ? specificCategory3Id : { in: category2Ids },
            ...(brandIds.length > 0 ? {
              productClass: {
                brand: {
                  afisat: true,
                  id: { in: brandIds }
                }
              }
            } : {}),
            ...(classIds.length > 0 ? { classId: { in: classIds } } : {}),
            ...(hasDiscount ? { HasDiscount: true } : {})
          },
          key: { not: null },
          value: { not: null }
        },
        _count: {
          Material_Number: true
        },
        orderBy: [
          { key: 'asc' },
          { value: 'asc' }
        ]
      }))
    ])

    // Process products
    const processedProducts: ProductCardInterface[] = products.map(product => ({
      id: product.id,
      Material_Number: product.Material_Number,
      Description_Local: product.Description_Local,
      ImageUrl: product.ImageUrl,
      FinalPrice: toSafeNumber(product.FinalPrice),
      PretAM: toSafeNumber(product.PretAM),
      HasDiscount: product.HasDiscount,
      activeDiscountType: product.activeDiscountType,
      activeDiscountValue: toSafeNumber(product.activeDiscountValue),
      discountPercentage: toSafeNumber(product.discountPercentage),
      categoryLevel3: product.categoryLevel3,
      productClass: product.productClass
    }))

    // Process categories
    const processedCategories: CategoryData[] = categories.map(cat => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      nameRO: cat.nameRO,
      productCount: cat._count.products
    }))

    // Process brands
    const processedBrands: BrandData[] = brands_data.map(brand => ({
      id: brand.id,
      name: brand.name,
      productCount: brand.productClasses.reduce((sum, pc) => sum + pc._count.products, 0)
    })).filter(brand => brand.productCount > 0)

    // Process classes - create individual entries for each VehicleModel (same as search)
    const processedClasses: ClassData[] = classes_data.flatMap(cls => {
      // Get all vehicle model names for this class
      const vehicleModelNames = cls.vehicleModels.map(vm => vm.vehicleModel.name)

      if (vehicleModelNames.length > 0) {
        // Create separate entries for each vehicle model
        return vehicleModelNames.map(modelName => ({
          id: `${cls.id}-${modelName}`, // Unique ID combining class ID and model name
          name: modelName,
          productCount: cls._count.products,
          brandId: cls.brand.id, // Add brand ID for filtering
          originalClassId: cls.id // Keep original class ID for filtering
        }))
      } else {
        // Fallback to brand-classCode if no models
        return [{
          id: cls.id,
          name: `${cls.brand.name} - ${cls.classCode}`,
          productCount: cls._count.products,
          brandId: cls.brand.id,
          originalClassId: cls.id
        }]
      }
    }).filter(cls => cls.productCount > 0)

    // Process attributes data
    const processedAttributes: AttributeData[] = []

    // Group attributes by key
    const attributesByKey = new Map<string, Array<{ value: string; productCount: number }>>()

    attributes_data.forEach(attr => {
      if (!attr.key || !attr.value) return

      if (!attributesByKey.has(attr.key)) {
        attributesByKey.set(attr.key, [])
      }

      attributesByKey.get(attr.key)!.push({
        value: attr.value,
        productCount: attr._count.Material_Number
      })
    })

    // Convert to final format
    attributesByKey.forEach((values, key) => {
      processedAttributes.push({
        key,
        values: values.sort((a, b) => b.productCount - a.productCount) // Sort by count desc
      })
    })

    // Sort attributes by key name
    processedAttributes.sort((a, b) => a.key.localeCompare(b.key))

    // Calculate pagination
    const totalPages = Math.ceil(totalCount / pageSize)

    // Get selected category info if specificCategory3Id is set
    let selectedCategory: { id: string; name: string } | undefined
    if (specificCategory3Id) {
      const categoryInfo = await withRetry(() => prisma.categoryLevel3.findUnique({
        where: { id: specificCategory3Id },
        select: { id: true, name: true, nameRO: true }
      }))
      if (categoryInfo) {
        selectedCategory = {
          id: categoryInfo.id,
          name: categoryInfo.nameRO || categoryInfo.name // Use nameRO when available
        }
      }
    }

    return {
      products: processedProducts,
      categories: processedCategories,
      brands: processedBrands,
      classes: processedClasses,
      attributes: processedAttributes,
      priceRange: {
        min: toSafeNumber(priceRange._min.FinalPrice) || 0,
        max: toSafeNumber(priceRange._max.FinalPrice) || 1000
      },
      pagination: {
        total: totalCount,
        pages: totalPages,
        currentPage: page,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      appliedFilters: filters,
      selectedCategory,
      has4th
    }

  } catch (error) {
    logger.error('Error fetching categories page data:', error)

    // Return empty results on error
    return {
      products: [],
      categories: [],
      brands: [],
      classes: [],
      attributes: [],
      priceRange: { min: 0, max: 1000 },
      pagination: {
        total: 0,
        pages: 0,
        currentPage: 1,
        hasNext: false,
        hasPrev: false
      },
      appliedFilters: filters,
      selectedCategory: undefined,
      has4th
    }
  }
}

export async function getSearchPageData(filters: SearchFilters): Promise<SearchPageData> {
  const {
    query = '',
    category3,
    category3Id,
    brands = [],
    classes = [],
    attributes = {},
    minPrice,
    maxPrice,
    hasDiscount,
    page = 1,
    sort = 'relevance'
  } = filters

  // Get user authentication and role information
  const user = await getCurrentDbUser()
  if (!user) {
    logger.error(`[getSearchPageData] No user authenticated`);
    return {
      products: [],
      categories: [],
      brands: [],
      classes: [],
      attributes: [],
      priceRange: { min: 0, max: 1000 },
      pagination: {
        total: 0,
        pages: 0,
        currentPage: 1,
        hasNext: false,
        hasPrev: false
      },
      appliedFilters: filters,
      selectedCategory: undefined,
      has4th: false
    }
  }
  const has4th = user.role.includes("fourLvlAdminAB") || user.role.includes("fourLvlInregistratAB")

  const pageSize = 24
  const skip = (page - 1) * pageSize

  // Use category3Id if provided, otherwise convert category3 name to ID for filtering
  let categoryId: string | undefined = undefined
  if (category3Id) {
    categoryId = category3Id
  } else if (category3) {
    const categoryRecord = await withRetry(() => prisma.categoryLevel3.findFirst({
      where: { 
        name: category3,
        afisat: true
       },
      select: { id: true }
    }))
    categoryId = categoryRecord?.id
  }

  // Convert brand names to IDs for filtering
  let brandIds: string[] = []
  if (brands.length > 0) {
    const brandRecords = await withRetry(() => prisma.brand.findMany({
      where: { 
        name: { in: brands },
        afisat: true
       },
      select: { id: true }
    }))
    brandIds = brandRecords.map(b => b.id)
  }

  // Convert class names to original class IDs for filtering
  let classIds: string[] = []
  if (classes.length > 0) {
    const classRecords = await withRetry(() => prisma.productClass.findMany({
      where: {
        vehicleModels: {
          some: {
            vehicleModel: {
              name: { in: classes },
              afisat: true
            }
          }
        }
      },
      select: { id: true }
    }))
    classIds = classRecords.map(c => c.id)
  }

  try {
    // Build where clause with proper typing
    const whereClause: Prisma.ProductWhereInput = {
      isActive: true,
      AND: []
    }

    // Search query
    if (query && query.length >= 3) {
      //whereClause.AND!.push({
      (whereClause.AND as Prisma.ProductWhereInput[]).push({
        OR: [
          { Material_Number: { contains: query } },
          { Description_Local: { contains: query } },
        ]
      })
    }

    // Category filter
    if (categoryId) {
      //whereClause.AND!.push({
      (whereClause.AND as Prisma.ProductWhereInput[]).push({
        categoryLevel3Id: categoryId
      })
    }

    // Brand filter (through ProductClass)
    if (brandIds.length > 0) {
      //whereClause.AND!.push({
      (whereClause.AND as Prisma.ProductWhereInput[]).push({
        productClass: {
          brand: {
            afisat: true,
            id: { in: brandIds }
          }
        },
      })
    }

    // Class filter
    if (classIds.length > 0) {
      //whereClause.AND!.push({
      (whereClause.AND as Prisma.ProductWhereInput[]).push({
        classId: { in: classIds }
      })
    }

    // Attributes filter
    if (Object.keys(attributes).length > 0) {
      Object.entries(attributes).forEach(([key, values]) => {
        if (values.length > 0) {
          //whereClause.AND!.push({
          (whereClause.AND as Prisma.ProductWhereInput[]).push({
            attributes: {
              some: {
                key: key,
                value: { in: values }
              }
            }
          })
        }
      })
    }

    if (minPrice !== undefined || maxPrice !== undefined) {
      const priceFilter: Prisma.DecimalFilter = {};
      if (minPrice !== undefined) priceFilter.gte = minPrice;
      if (maxPrice !== undefined) priceFilter.lte = maxPrice;
      (whereClause.AND as Prisma.ProductWhereInput[]).push({
        FinalPrice: priceFilter
      });
    }

    // Discount filter
    if (hasDiscount) {
      (whereClause.AND as Prisma.ProductWhereInput[]).push({
        HasDiscount: true
      })
    }

    // Build order by clause with proper typing
    let orderBy: Prisma.ProductOrderByWithRelationInput[] = []
    switch (sort) {
      case 'price_asc':
        orderBy = [{ FinalPrice: 'asc' }]
        break
      case 'price_desc':
        orderBy = [{ FinalPrice: 'desc' }]
        break
      case 'discount_desc':
        orderBy = [{ discountPercentage: 'desc' }, { FinalPrice: 'asc' }]
        break
      case 'name_asc':
        orderBy = [{ Description_Local: 'asc' }]
        break
      case 'relevance':
      default:
        orderBy = [
          { HasDiscount: 'desc' },
          { createdAt: 'desc' },
          { Description_Local: 'asc' }
        ]
        break
    }

    // Execute queries in parallel
    const [products, totalCount, categories, brands_data, classes_data, priceRange, attributes_data] = await Promise.all([
      // Products
      withRetry(() => prisma.product.findMany({
        where: whereClause,
        select: {
          id: true,
          Material_Number: true,
          Description_Local: true,
          ImageUrl: true,
          FinalPrice: true,
          PretAM: true,
          HasDiscount: true,
          activeDiscountType: true,
          activeDiscountValue: true,
          discountPercentage: true,
          categoryLevel3: {
            where: {
              afisat: true
            },
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          productClass: {
            select: {
              id: true,
              classCode: true,
              brand: {
                select: {
                  id: true,
                  name: true
                }
              },
              vehicleModels: {
                select: {
                  vehicleModel: {
                    select: {
                      name: true
                    }
                  }
                },
                where: {
                  vehicleModel: {
                    afisat: true
                  }
                }
              }
            }
          }
        },
        orderBy,
        skip,
        take: pageSize
      })),

      // Total count
      withRetry(() => prisma.product.count({
        where: whereClause
      })),

      // Categories with counts
      withRetry(() => prisma.categoryLevel3.findMany({
        where: {
          products: {
            some: {
              isActive: true,
              ...(query && query.length >= 3 ? {
                OR: [
                  { Material_Number: { contains: query } },
                  { Description_Local: { contains: query } }
                ]
              } : {})
            }
          },
          afisat: true,
          isActive: true
        },
        select: {
          id: true,
          name: true,
          nameRO: true,
          slug: true,
          _count: {
            select: {
              products: {
                where: {
                  isActive: true,
                  ...(query && query.length >= 3 ? {
                    OR: [
                      { Material_Number: { contains: query } },
                      { Description_Local: { contains: query } }
                    ]
                  } : {})
                }
              }
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      })),

      // Brands with counts
      withRetry(() => prisma.brand.findMany({
        where: {
          productClasses: {
            some: {
              products: {
                some: {
                  isActive: true,
                  ...(categoryId ? { categoryLevel3Id: categoryId } : {}),
                  ...(query && query.length >= 3 ? {
                    OR: [
                      { Material_Number: { contains: query } },
                      { Description_Local: { contains: query } }
                    ]
                  } : {})
                }
              }
            }
          },
          afisat: true
        },
        select: {
          id: true,
          name: true,
          productClasses: {
            select: {
              _count: {
                select: {
                  products: {
                    where: {
                      isActive: true,
                      ...(categoryId ? { categoryLevel3Id: categoryId } : {}),
                      ...(query && query.length >= 3 ? {
                        OR: [
                          { Material_Number: { contains: query } },
                          { Description_Local: { contains: query } }
                        ]
                      } : {})
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      })),

      // Classes with counts
      withRetry(() => prisma.productClass.findMany({
        where: {
          products: {
            some: {
              isActive: true,
              ...(categoryId ? { categoryLevel3Id: categoryId } : {}),
              ...(query && query.length >= 3 ? {
                OR: [
                  { Material_Number: { contains: query } },
                  { Description_Local: { contains: query } }
                ]
              } : {})
            }
          }
        },
        select: {
          id: true,
          classCode: true,
          brand: {
            select: {
              afisat: true,
              id: true,
              name: true
            }
          },
          vehicleModels: {
            where: {
              vehicleModel: {
                afisat: true
              }
            },
            select: {
              vehicleModel: {
                select: {
                  name: true
                }
              }
            }
          },
          _count: {
            select: {
              products: {
                where: {
                  isActive: true,
                  ...(categoryId ? { categoryLevel3Id: categoryId } : {}),
                  ...(query && query.length >= 3 ? {
                    OR: [
                      { Material_Number: { contains: query } },
                      { Description_Local: { contains: query } }
                    ]
                  } : {})
                }
              }
            }
          }
        },
        orderBy: {
          classCode: 'asc'
        }
      })),

      // Price range
      withRetry(() => prisma.product.aggregate({
        where: {
          isActive: true,
          FinalPrice: { not: null },
          ...(categoryId ? { categoryLevel3Id: categoryId } : {}),
          ...(brandIds.length > 0 ? {
            productClass: {
              brand: {
                afisat: true,
                id: { in: brandIds }
              }
            }
          } : {}),
          ...(classIds.length > 0 ? { classId: { in: classIds } } : {}),
          ...(hasDiscount ? { HasDiscount: true } : {}),
          ...(query && query.length >= 3 ? {
            OR: [
              { Material_Number: { contains: query } },
              { Description_Local: { contains: query } }
            ]
          } : {})
        },
        _min: {
          FinalPrice: true
        },
        _max: {
          FinalPrice: true
        }
      })),

      // Attributes
      withRetry(() => prisma.productAttribute.groupBy({
        by: ['key', 'value'],
        where: {
          product: {
            isActive: true,
            ...(categoryId ? { categoryLevel3Id: categoryId } : {}),
            ...(brandIds.length > 0 ? {
              productClass: {
                brand: {
                  afisat: true,
                  id: { in: brandIds }
                }
              }
            } : {}),
            ...(classIds.length > 0 ? { classId: { in: classIds } } : {}),
            ...(hasDiscount ? { HasDiscount: true } : {}),
            ...(query && query.length >= 3 ? {
              OR: [
                { Material_Number: { contains: query } },
                { Description_Local: { contains: query } }
              ]
            } : {})
          },
          key: { not: null },
          value: { not: null }
        },
        _count: {
          Material_Number: true
        },
        orderBy: [
          { key: 'asc' },
          { value: 'asc' }
        ]
      }))
    ])

    // Process products
    const processedProducts: ProductCardInterface[] = products.map(product => ({
      id: product.id,
      Material_Number: product.Material_Number,
      Description_Local: product.Description_Local,
      ImageUrl: product.ImageUrl,
      FinalPrice: toSafeNumber(product.FinalPrice),
      PretAM: toSafeNumber(product.PretAM),
      HasDiscount: product.HasDiscount,
      activeDiscountType: product.activeDiscountType,
      activeDiscountValue: toSafeNumber(product.activeDiscountValue),
      discountPercentage: toSafeNumber(product.discountPercentage),
      categoryLevel3: product.categoryLevel3,
      productClass: product.productClass
    }))

    // Process categories
    const processedCategories: CategoryData[] = categories.map(cat => ({
      id: cat.id,
      name: cat.name,
      nameRO: cat.nameRO,
      slug: cat.slug,
      productCount: cat._count.products
    }))

    // Process brands
    const processedBrands: BrandData[] = brands_data.map(brand => ({
      id: brand.id,
      name: brand.name,
      productCount: brand.productClasses.reduce((total, pc) => total + pc._count.products, 0)
    }))

    // Process classes - create individual entries for each VehicleModel
    const processedClasses: ClassData[] = classes_data.flatMap(cls => {
      // Get all vehicle model names for this class
      const vehicleModelNames = cls.vehicleModels.map(vm => vm.vehicleModel.name)

      if (vehicleModelNames.length > 0) {
        // Create separate entries for each vehicle model
        return vehicleModelNames.map(modelName => ({
          id: `${cls.id}-${modelName}`, // Unique ID combining class ID and model name
          name: modelName,
          productCount: cls._count.products,
          brandId: cls.brand.id, // Add brand ID for filtering
          originalClassId: cls.id // Keep original class ID for filtering
        }))
      } else {
        // Fallback to brand-classCode if no models
        return [{
          id: cls.id,
          name: `${cls.brand.name} - ${cls.classCode}`,
          productCount: cls._count.products,
          brandId: cls.brand.id,
          originalClassId: cls.id
        }]
      }
    })

    // Calculate pagination
    const totalPages = Math.ceil(totalCount / pageSize)

    // Process attributes data
    const processedAttributes: AttributeData[] = []

    // Group attributes by key
    const attributesByKey = new Map<string, Array<{ value: string; productCount: number }>>()

    attributes_data.forEach(attr => {
      if (!attr.key || !attr.value) return

      if (!attributesByKey.has(attr.key)) {
        attributesByKey.set(attr.key, [])
      }

      attributesByKey.get(attr.key)!.push({
        value: attr.value,
        productCount: attr._count.Material_Number
      })
    })

    // Convert to final format
    attributesByKey.forEach((values, key) => {
      processedAttributes.push({
        key,
        values: values.sort((a, b) => b.productCount - a.productCount) // Sort by count desc
      })
    })

    // Sort attributes by key name
    processedAttributes.sort((a, b) => a.key.localeCompare(b.key))

    // Get selected category info if categoryId is set
    let selectedCategory: { id: string; name: string } | undefined
    if (categoryId) {
      const categoryInfo = await withRetry(() => prisma.categoryLevel3.findUnique({
        where: { id: categoryId },
        select: { id: true, name: true, nameRO: true }
      }))
      if (categoryInfo) {
        selectedCategory = categoryInfo
      }
    }

    return {
      products: processedProducts,
      categories: processedCategories,
      brands: processedBrands,
      classes: processedClasses,
      attributes: processedAttributes,
      priceRange: {
        min: toSafeNumber(priceRange._min.FinalPrice) || 0,
        max: toSafeNumber(priceRange._max.FinalPrice) || 1000
      },
      pagination: {
        total: totalCount,
        pages: totalPages,
        currentPage: page,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      appliedFilters: filters,
      selectedCategory,
      has4th
    }

  } catch (error) {
    logger.error('Error fetching search page data:', error)

    // Return empty results on error
    return {
      products: [],
      categories: [],
      brands: [],
      classes: [],
      attributes: [],
      priceRange: { min: 0, max: 1000 },
      pagination: {
        total: 0,
        pages: 0,
        currentPage: 1,
        hasNext: false,
        hasPrev: false
      },
      appliedFilters: filters,
      selectedCategory: undefined,
      has4th
    }
  }
}

// export async function getSearchPageData(filters: SearchFilters): Promise<SearchPageData> {
//   const {
//     query = '',
//     category3,
//     category3Id,
//     brands = [],
//     classes = [],
//     attributes = {},
//     minPrice,
//     maxPrice,
//     hasDiscount,
//     page = 1,
//     sort = 'relevance'
//   } = filters;

//   const pageSize = 24;
//   const skip = (page - 1) * pageSize;

//   // Use category3Id if provided, otherwise convert category3 name to ID for filtering
//   let resolvedCategoryId: string | undefined = undefined;
//   if (category3Id) {
//     resolvedCategoryId = category3Id;
//   } else if (category3) {
//     const categoryRecord = await withRetry(() => prisma.categoryLevel3.findFirst({
//       where: { name: category3 },
//       select: { id: true }
//     }));
//     resolvedCategoryId = categoryRecord?.id;
//   }

//   // Convert brand names to IDs for filtering
//   let brandIds: string[] = [];
//   if (brands.length > 0) {
//     const brandRecords = await withRetry(() => prisma.brand.findMany({
//       where: { name: { in: brands } },
//       select: { id: true }
//     }));
//     brandIds = brandRecords.map(b => b.id);
//   }

//   // Convert class names (vehicle models) to original ProductClass IDs for filtering
//   let classIds: string[] = [];
//   if (classes.length > 0) {
//     const classRecords = await withRetry(() => prisma.productClass.findMany({
//       where: {
//         vehicleModels: {
//           some: {
//             vehicleModel: {
//               name: { in: classes }
//             }
//           }
//         }
//       },
//       select: { id: true }
//     }));
//     classIds = classRecords.map(c => c.id);
//   }

//   try {
//     // Build where clause using Prisma's generated type for type safety
//     const whereClause: Prisma.ProductWhereInput = {
//       isActive: true,
//       AND: []
//     };

//     // Search query
//     if (query && query.length >= 3) {
//       (whereClause.AND as Prisma.ProductWhereInput[]).push({
//         OR: [
//           { Material_Number: { contains: query.toLowerCase() } },
//           { Description_Local: { contains: query.toLowerCase() } }
//         ]
//       });
//     }

//     // Category filter
//     if (resolvedCategoryId) {
//       (whereClause.AND as Prisma.ProductWhereInput[]).push({
//         categoryLevel3Id: resolvedCategoryId
//       });
//     }

//     // Brand filter (through ProductClass)
//     if (brandIds.length > 0) {
//       (whereClause.AND as Prisma.ProductWhereInput[]).push({
//         productClass: {
//           brand: {
//             id: { in: brandIds }
//           }
//         }
//       });
//     }

//     // Class filter
//     if (classIds.length > 0) {
//       (whereClause.AND as Prisma.ProductWhereInput[]).push({
//         classId: { in: classIds }
//       });
//     }

//     // Attributes filter
//     if (Object.keys(attributes).length > 0) {
//       Object.entries(attributes).forEach(([key, values]) => {
//         if (values.length > 0) {
//           (whereClause.AND as Prisma.ProductWhereInput[]).push({
//             attributes: {
//               some: {
//                 key: key,
//                 value: { in: values }
//               }
//             }
//           });
//         }
//       });
//     }

//     // Price range filter
//     if (minPrice !== undefined || maxPrice !== undefined) {
//       const priceFilter: Prisma.DecimalFilter = {};
//       if (minPrice !== undefined) priceFilter.gte = minPrice;
//       if (maxPrice !== undefined) priceFilter.lte = maxPrice;
//       (whereClause.AND as Prisma.ProductWhereInput[]).push({
//         FinalPrice: priceFilter
//       });
//     }

//     // Discount filter
//     if (hasDiscount) {
//       (whereClause.AND as Prisma.ProductWhereInput[]).push({
//         HasDiscount: true
//       });
//     }

//     // Build order by clause
//     const orderBy: Prisma.ProductOrderByWithRelationInput[] = [];
//     switch (sort) {
//       case 'price_asc':
//         orderBy.push({ FinalPrice: 'asc' });
//         break;
//       case 'price_desc':
//         orderBy.push({ FinalPrice: 'desc' });
//         break;
//       case 'discount_desc':
//         orderBy.push({ discountPercentage: 'desc' }, { FinalPrice: 'asc' });
//         break;
//       case 'name_asc':
//         orderBy.push({ Description_Local: 'asc' });
//         break;
//       case 'relevance':
//       default:
//         orderBy.push(
//           { HasDiscount: 'desc' },
//           { createdAt: 'desc' },
//           { Description_Local: 'asc' }
//         );
//         break;
//     }

//     // Use a common where clause for fetching filter options for consistency
//     const filterWhereClause: Prisma.ProductWhereInput = {
//       isActive: true,
//       ...(resolvedCategoryId ? { categoryLevel3Id: resolvedCategoryId } : {}),
//       ...(query && query.length >= 3 ? {
//         OR: [
//           { Material_Number: { contains: query.toLowerCase() } },
//           { Description_Local: { contains: query.toLowerCase() } }
//         ]
//       } : {})
//     };

//     // Execute queries in parallel
//     const [products, totalCount, categories, brands_data, classes_data, priceRange, attributes_data] = await Promise.all([
//       // Products
//       withRetry(() => prisma.product.findMany({
//         where: whereClause,
//         select: {
//           id: true,
//           Material_Number: true,
//           Description_Local: true,
//           ImageUrl: true,
//           FinalPrice: true,
//           PretAM: true,
//           HasDiscount: true,
//           activeDiscountType: true,
//           activeDiscountValue: true,
//           discountPercentage: true,
//           categoryLevel3: { select: { id: true, name: true, slug: true } },
//           productClass: {
//             select: {
//               id: true,
//               classCode: true,
//               brand: { select: { id: true, name: true } },
//               vehicleModels: { select: { vehicleModel: { select: { name: true } } } }
//             }
//           }
//         },
//         orderBy,
//         skip,
//         take: pageSize
//       })),

//       // Total count
//       withRetry(() => prisma.product.count({ where: whereClause })),

//       // Categories with counts
//       withRetry(() => prisma.categoryLevel3.findMany({
//         where: { products: { some: filterWhereClause } },
//         select: {
//           id: true, name: true, slug: true,
//           _count: { select: { products: { where: filterWhereClause } } }
//         },
//         orderBy: { name: 'asc' }
//       })),

//       // Brands with counts
//       withRetry(() => prisma.brand.findMany({
//         where: { productClasses: { some: { products: { some: filterWhereClause } } } },
//         select: {
//           id: true, name: true,
//           productClasses: { select: { _count: { select: { products: { where: filterWhereClause } } } } }
//         },
//         orderBy: { name: 'asc' }
//       })),

//       // Classes with counts
//       withRetry(() => prisma.productClass.findMany({
//         where: { products: { some: filterWhereClause } },
//         select: {
//           id: true, classCode: true,
//           brand: { select: { id: true, name: true } },
//           vehicleModels: { select: { vehicleModel: { select: { name: true } } } },
//           _count: { select: { products: { where: filterWhereClause } } }
//         },
//         orderBy: { classCode: 'asc' }
//       })),

//       // Price range - based on current filters (excluding price)
//       withRetry(() => prisma.product.aggregate({
//         where: { ...whereClause, FinalPrice: undefined }, // Reuse where clause but ignore price
//         _min: { FinalPrice: true },
//         _max: { FinalPrice: true }
//       })),

//       // Attributes - get unique key-value pairs with counts
//       withRetry(() => prisma.productAttribute.groupBy({
//         by: ['key', 'value'],
//         where: {
//           product: whereClause, // Reuse the main where clause
//           key: { not: null },
//           value: { not: null }
//         },
//         _count: { Material_Number: true },
//         orderBy: [{ key: 'asc' }, { value: 'asc' }]
//       }))
//     ]);

//     // Process products
//     const processedProducts: ProductCardInterface[] = products.map(product => ({
//       ...product,
//       FinalPrice: toSafeNumber(product.FinalPrice),
//       PretAM: toSafeNumber(product.PretAM),
//       activeDiscountValue: toSafeNumber(product.activeDiscountValue),
//       discountPercentage: toSafeNumber(product.discountPercentage),
//     }));

//     // Process categories
//     const processedCategories: CategoryFilterItem[] = categories.map(cat => ({
//       id: cat.id, name: cat.name, slug: cat.slug,
//       productCount: cat._count.products
//     }));
    

//     // Process brands
//     const processedBrands: BrandFilterItem[] = brands_data.map(brand => ({
//       id: brand.id, name: brand.name,
//       productCount: brand.productClasses.reduce((total, pc) => total + pc._count.products, 0)
//     }));

//     // Process classes
//     const processedClasses: ClassFilterItem[] = classes_data.flatMap(cls => {
//       const vehicleModelNames = cls.vehicleModels.map(vm => vm.vehicleModel.name);
//       const productCount = cls._count.products;

//       if (!cls.brand) return []; // Skip if a class has no associated brand

//       if (vehicleModelNames.length > 0) {
//         return vehicleModelNames.map(modelName => ({
//           id: `${cls.id}-${modelName}`, name: modelName, productCount,
//           brandId: cls.brand!.id, originalClassId: cls.id
//         }));
//       } else {
//         return [{
//           id: cls.id, name: `${cls.brand.name} - ${cls.classCode}`, productCount,
//           brandId: cls.brand.id, originalClassId: cls.id
//         }];
//       }
//     });

//     // Process attributes data
//     const attributesByKey = new Map<string, { value: string; productCount: number }[]>();
//     attributes_data.forEach(attr => {
//       if (!attr.key || !attr.value) return;
//       if (!attributesByKey.has(attr.key)) {
//         attributesByKey.set(attr.key, []);
//       }
//       attributesByKey.get(attr.key)!.push({
//         value: attr.value,
//         productCount: attr._count.Material_Number
//       });
//     });
    
//     const processedAttributes: AttributeFilter[] = [];
//     attributesByKey.forEach((values, key) => {
//       processedAttributes.push({
//         key,
//         values: values.sort((a, b) => b.productCount - a.productCount)
//       });
//     });
//     processedAttributes.sort((a, b) => a.key.localeCompare(b.key));

//     // Get selected category info if categoryId is set
//     let selectedCategory: { id: string; name: string } | undefined;
//     if (resolvedCategoryId) {
//       selectedCategory = await withRetry(() => prisma.categoryLevel3.findUnique({
//         where: { id: resolvedCategoryId },
//         select: { id: true, name: true }
//       })) ?? undefined;
//     }

//     return {
//       products: processedProducts,
//       categories: processedCategories,
//       brands: processedBrands,
//       classes: processedClasses,
//       attributes: processedAttributes,
//       priceRange: {
//         min: toSafeNumber(priceRange._min.FinalPrice) || 0,
//         max: toSafeNumber(priceRange._max.FinalPrice) || 10000 // Fallback max price
//       },
//       pagination: {
//         total: totalCount,
//         pages: Math.ceil(totalCount / pageSize),
//         currentPage: page,
//         hasNext: page * pageSize < totalCount,
//         hasPrev: page > 1
//       },
//       appliedFilters: filters,
//       selectedCategory
//     };

//   } catch (error) {
//     logger.error('Error fetching search page data:', error);

//     // Return empty results on error, conforming to the SearchPageData interface
//     return {
//       products: [],
//       categories: [],
//       brands: [],
//       classes: [],
//       attributes: [],
//       priceRange: { min: 0, max: 10000 },
//       pagination: { total: 0, pages: 0, currentPage: 1, hasNext: false, hasPrev: false },
//       appliedFilters: filters,
//       selectedCategory: undefined
//     };
//   }
// }