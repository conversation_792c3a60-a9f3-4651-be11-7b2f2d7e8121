// app/(public)/api/images/banners/[imageIdentifier]/route.ts
import { NextRequest, NextResponse } from "next/server";
import { promises as fs } from "fs";
import path from "path";
import { createHash } from "crypto";
import { prisma, withRetry } from "@/lib/db";
import { logger } from "@/lib/logger";
import { headers } from "next/headers";

// Security constants
const ALLOWED_EXTENSIONS = [".jpg", ".jpeg", ".png", ".webp", ".gif"];
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 100;

// In-memory rate limiting (use Redis in production)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

// Content Security Headers
const SECURITY_HEADERS = {
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",
  "Referrer-Policy": "strict-origin-when-cross-origin",
  "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
};

/**
 * Rate limiting middleware
 */
function checkRateLimit(identifier: string): boolean {
  const now = Date.now();
  const limit = rateLimitMap.get(identifier);

  if (!limit || now > limit.resetTime) {
    rateLimitMap.set(identifier, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW,
    });
    return true;
  }

  if (limit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }

  limit.count++;
  return true;
}

/**
 * Sanitize and validate imageIdentifier
 */
function validateImageIdentifier(imageIdentifier: string): {
  valid: boolean;
  sanitized?: string;
  error?: string;
} {
  // Remove any whitespace
  const trimmed = imageIdentifier.trim();

  // Check length (reasonable UUID or identifier length)
  if (trimmed.length < 1 || trimmed.length > 255) {
    return { valid: false, error: "Invalid identifier length" };
  }

  // Only allow alphanumeric, hyphens, and underscores
  const sanitized = trimmed.replace(/[^a-zA-Z0-9\-_]/g, "");
  
  if (sanitized !== trimmed) {
    return { valid: false, error: "Invalid characters in identifier" };
  }

  // Prevent path traversal attempts
  if (sanitized.includes("..") || sanitized.includes("/") || sanitized.includes("\\")) {
    return { valid: false, error: "Path traversal detected" };
  }

  return { valid: true, sanitized };
}

/**
 * Validate file extension
 */
function validateFileExtension(filename: string): boolean {
  const ext = path.extname(filename).toLowerCase();
  return ALLOWED_EXTENSIONS.includes(ext);
}

/**
 * Get content type from extension
 */
function getContentType(filename: string): string {
  const ext = path.extname(filename).toLowerCase();
  const contentTypes: Record<string, string> = {
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".png": "image/png",
    ".webp": "image/webp",
    ".gif": "image/gif",
  };
  return contentTypes[ext] || "application/octet-stream";
}

/**
 * Generate ETag for caching
 */
function generateETag(buffer: Buffer): string {
  return `"${createHash("md5").update(buffer).digest("hex")}"`;
}

/**
 * Check if file is within allowed directory (security check)
 */
function isPathSafe(filePath: string, baseDir: string): boolean {
  const normalizedBase = path.resolve(baseDir);
  const normalizedPath = path.resolve(filePath);
  return normalizedPath.startsWith(normalizedBase);
}

/**
 * GET handler - Serve banner images
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ imageIdentifier: string }> }
) {
  const startTime = Date.now();
  
  try {
    // 1. Environment validation
    if (!process.env.BANNERS_BASE_PATH) {
      logger.error("BANNERS_STORAGE environment variable not defined");
      return NextResponse.json(
        { error: "Service configuration error" },
        { status: 503, headers: SECURITY_HEADERS }
      );
    }

    // 2. Get client IP for rate limiting
    const headersList = await headers();
    const forwardedFor = headersList.get("x-forwarded-for");
    const realIp = headersList.get("x-real-ip");
    const clientIp = forwardedFor?.split(",")[0] || realIp || "unknown";

    // 3. Rate limiting
    if (!checkRateLimit(clientIp)) {
      logger.warn(`Rate limit exceeded for IP: ${clientIp}`);
      return NextResponse.json(
        { 
          error: "Too many requests",
          retryAfter: Math.ceil(RATE_LIMIT_WINDOW / 1000)
        },
        { 
          status: 429,
          headers: {
            ...SECURITY_HEADERS,
            "Retry-After": String(Math.ceil(RATE_LIMIT_WINDOW / 1000)),
          }
        }
      );
    }

    // 4. Extract and validate params
    const paramsObject = await params;
    const { imageIdentifier } = paramsObject;

    // 5. Validate and sanitize imageIdentifier
    const validation = validateImageIdentifier(imageIdentifier);
    if (!validation.valid) {
      logger.warn(`Invalid imageIdentifier: ${imageIdentifier} - ${validation.error}`);
      return NextResponse.json(
        { error: "Invalid image identifier" },
        { status: 400, headers: SECURITY_HEADERS }
      );
    }

    // 6. Database lookup with timeout
    const banner = await Promise.race([
      withRetry(() =>
        prisma.banner.findFirst({
          where: { imageIdentifier: validation.sanitized },
          select: {
            imageFilename: true,
            title: true,
            isActive: true, // Ensure banner is active
          },
        })
      ),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Database timeout")), 5000)
      ),
    ]) as { imageFilename: string; title: string; isActive: boolean } | null;

    // 7. Check if banner exists and is active
    if (!banner || !banner.imageFilename || !banner.isActive) {
      logger.warn(`Banner not found or inactive: ${validation.sanitized}`);
      return NextResponse.json(
        { error: "Image not found" },
        { status: 404, headers: SECURITY_HEADERS }
      );
    }

    // 8. Validate file extension
    if (!validateFileExtension(banner.imageFilename)) {
      logger.error(`Invalid file extension: ${banner.imageFilename}`);
      return NextResponse.json(
        { error: "Invalid file type" },
        { status: 400, headers: SECURITY_HEADERS }
      );
    }

    // 9. Construct file path with security checks
    const bannersDir = path.resolve(process.env.BANNERS_BASE_PATH);
    const fullPath = path.join(bannersDir, banner.imageFilename);

    // 10. Path traversal protection
    if (!isPathSafe(fullPath, bannersDir)) {
      logger.error(`Path traversal attempt detected: ${imageIdentifier}`);
      return NextResponse.json(
        { error: "Access denied" },
        { status: 403, headers: SECURITY_HEADERS }
      );
    }

    // 11. Check file existence and get stats
    let fileStats;
    try {
      fileStats = await fs.stat(fullPath);
      
      // Additional security: check if it's actually a file
      if (!fileStats.isFile()) {
        throw new Error("Not a file");
      }

      // Check file size
      if (fileStats.size > MAX_FILE_SIZE) {
        logger.error(`File too large: ${banner.imageFilename} (${fileStats.size} bytes)`);
        return NextResponse.json(
          { error: "File too large" },
          { status: 413, headers: SECURITY_HEADERS }
        );
      }
    } catch (error) {
      logger.error(`File access error: ${fullPath}`, error);
      return NextResponse.json(
        { error: "Image not found" },
        { status: 404, headers: SECURITY_HEADERS }
      );
    }

    // 12. Check conditional requests (ETag, If-None-Match)
    const ifNoneMatch = request.headers.get("if-none-match");
    const fileBuffer = await fs.readFile(fullPath);
    const etag = generateETag(fileBuffer);

    if (ifNoneMatch === etag) {
      logger.info(`304 Not Modified: ${validation.sanitized}`);
      return new NextResponse(null, {
        status: 304,
        headers: {
          ...SECURITY_HEADERS,
          ETag: etag,
        },
      });
    }

    // 13. Prepare response headers
    const contentType = getContentType(banner.imageFilename);
    const responseHeaders = new Headers({
      ...SECURITY_HEADERS,
      "Content-Type": contentType,
      "Content-Length": fileBuffer.length.toString(),
      "Cache-Control": "public, max-age=31536000, immutable",
      ETag: etag,
      "Last-Modified": fileStats.mtime.toUTCString(),
      "Content-Disposition": `inline; filename="${path.basename(banner.imageFilename)}"`,
      "Accept-Ranges": "bytes",
    });

    // 14. Log successful access
    const duration = Date.now() - startTime;
    logger.info(
      `Image served: ${validation.sanitized} | File: ${banner.imageFilename} | Size: ${fileBuffer.length} | Duration: ${duration}ms | IP: ${clientIp}`
    );

    // 15. Return image
    return new NextResponse(fileBuffer as BodyInit, {
      status: 200,
      headers: responseHeaders,
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error(`Banner image API error (${duration}ms):`, error);
    
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500, headers: SECURITY_HEADERS }
    );
  }
}

// Explicitly handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { 
      status: 405,
      headers: {
        ...SECURITY_HEADERS,
        Allow: "GET",
      }
    }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { 
      status: 405,
      headers: {
        ...SECURITY_HEADERS,
        Allow: "GET",
      }
    }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { 
      status: 405,
      headers: {
        ...SECURITY_HEADERS,
        Allow: "GET",
      }
    }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { 
      status: 405,
      headers: {
        ...SECURITY_HEADERS,
        Allow: "GET",
      }
    }
  );
}

// Configure route segment
export const runtime = "nodejs"; // Use Node.js runtime for fs access
export const dynamic = "force-dynamic"; // Disable static optimization for dynamic routes