
import fsp from "fs/promises";
import path from "path";

interface ImageIndex {
[productId: string]: string[]; // productId -> array of image filenames
}

class ImageIndexManager {
private static instance: ImageIndexManager;
private imageIndex: ImageIndex = {};
private isBuilding = false;
private lastBuildTime = 0;
private readonly REBUILD_INTERVAL = 5 * 24 * 60 * 60 * 1000; // 5 days

static getInstance(): ImageIndexManager {
if (!ImageIndexManager.instance) {
ImageIndexManager.instance = new ImageIndexManager();
}
return ImageIndexManager.instance;
}

async getProductImages(productId: string): Promise<string[]> {
await this.ensureIndexBuilt();
return this.imageIndex[productId] || [];
}

private async ensureIndexBuilt() {
const now = Date.now();


if (this.isBuilding) {
  // Wait for current build to complete
  while (this.isBuilding) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  return;
}

if (now - this.lastBuildTime > this.REBUILD_INTERVAL || Object.keys(this.imageIndex).length === 0) {
  await this.buildIndex();
}
}

private async buildIndex() {
if (this.isBuilding) return;


this.isBuilding = true;
console.log('Building image index...');
const startTime = Date.now();

try {
  const BASE_DIR = process.env.PICTURES_BASE_PATH;
  if (!BASE_DIR) throw new Error("PICTURES_BASE_PATH not defined");

  const files = await fsp.readdir(BASE_DIR);
  const ALLOWED_EXT = new Set([".jpg", ".jpeg", ".png", ".webp", ".gif", ".avif", ".bmp", ".mp4"]);
  
  const newIndex: ImageIndex = {};

  for (const file of files) {
    const ext = path.extname(file).toLowerCase();
    if (!ALLOWED_EXT.has(ext)) continue;

    const nameWithoutExt = path.basename(file, ext);
    
    // Extract product ID from filename
    const productId = this.extractProductId(nameWithoutExt);
    if (productId) {
      if (!newIndex[productId]) {
        newIndex[productId] = [];
      }
      newIndex[productId].push(file);
    }
  }

  // Sort images for each product
  for (const productId in newIndex) {
    newIndex[productId].sort((a, b) => {
      const aName = path.basename(a, path.extname(a));
      const bName = path.basename(b, path.extname(b));
      
      // Main image (exact match) comes first
      if (aName === productId && bName !== productId) return -1;
      if (bName === productId && aName !== productId) return 1;
      
      return aName.localeCompare(bName);
    });
  }

  this.imageIndex = newIndex;
  this.lastBuildTime = Date.now();
  
  const buildTime = Date.now() - startTime;
  console.log(`Image index built: ${Object.keys(newIndex).length} products, ${buildTime}ms`);
  
} catch (error) {
  console.error('Failed to build image index:', error);
} finally {
  this.isBuilding = false;
}
}

private extractProductId(nameWithoutExt: string): string | null {
// Handle different naming patterns
const patterns = [
/^(\d+)$/, // exact match: 12345
/^([A-Z0-9]+)/, // with underscore: ABC123_01
/^(\w+)\d+$/ // SKU with number: SKU123_01
];


for (const pattern of patterns) {
  const match = nameWithoutExt.match(pattern);
  if (match) return match[1];
}

// If no pattern matches, return the whole name (for exact matches)
return nameWithoutExt;
}

// Force rebuild (useful for admin operations)
async forceRebuild() {
this.lastBuildTime = 0;
await this.buildIndex();
}
}

export const imageIndex = ImageIndexManager.getInstance(); 