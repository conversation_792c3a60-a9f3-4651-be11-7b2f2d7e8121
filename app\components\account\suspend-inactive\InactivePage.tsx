"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { UserX, Mail, Phone, ArrowLeft, AlertTriangle } from "lucide-react";
import Link from "next/link";

export default function AccountInactivePage() {

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-slate-50 flex items-center justify-center p-6">
      <div className="max-w-4xl mx-auto text-center">
        {/* Warning Icon */}
        <div className="mb-8">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-orange-100 rounded-full mb-6 animate-pulse">
            <UserX className="w-12 h-12 text-orange-600" />
          </div>
        </div>

        {/* Status Message */}
        <div className="mb-12 space-y-4">
          <Badge className="bg-orange-100 text-orange-800 px-4 py-2 text-sm font-semibold mb-4">
            Cont Inactiv
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-[#4D4D4D] mb-4">
            Contul tau este inactiv
          </h1>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Contul tau este inactiv. Acest lucru se poate întampla din motive de securitate sau pentru a preveni abuzuri.
          </p>
        </div>

        {/* Information Card */}
        <Card className="max-w-2xl mx-auto mb-8 shadow-xl border-0 bg-white">
          <CardContent className="p-8">
            <div className="space-y-6">
              <div className="flex items-center justify-center gap-3 mb-6">
                <AlertTriangle className="w-6 h-6 text-orange-600" />
                <h3 className="text-2xl font-bold text-[#4D4D4D]">
                  Status Cont
                </h3>
              </div>

              <div className="text-left space-y-4">
                <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                  <h4 className="font-semibold text-[#4D4D4D] mb-2">
                    De ce contul este inactiv?
                  </h4>
                  <ul className="text-sm text-slate-600 space-y-1">
                    <li>• Contul a fost marcat inactiv de catre administratorul site-ului</li>
                    <li>• Email-ul este in curs de verificare</li>
                    <li>• Informatiile contului necesita actualizare</li>
                  </ul>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-semibold text-[#4D4D4D] mb-2">
                    Cum puteti reactiva contul:
                  </h4>
                  <ul className="text-sm text-slate-600 space-y-1">
                    <li>• Contactati echipa noastra de asistenta clienti</li>
                    <li>• Verificati-va identitatea si informatiile contului</li>
                    <li>• Actualizati detaliile contului care nu mai sunt valabile</li>
                    <li>• Finalizati pasii de verificare ramasi in curs</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
          <Button
            onClick={() =>
              (window.location.href = "mailto:<EMAIL>")
            }
            className="bg-[#0066B1] hover:bg-[#0052A3] text-white px-8 py-3 text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            size="lg"
          >
            <Mail className="w-5 h-5 mr-2" />
            Email Suport
          </Button>

          <Button
            onClick={() => (window.location.href = "tel:******-BMW-PARTS")}
            variant="outline"
            className="border-[#0066B1] text-[#0066B1] hover:bg-[#0066B1] hover:text-white px-8 py-3 text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            size="lg"
          >
            <Phone className="w-5 h-5 mr-2" />
            Contactati Asistenta Telefonica
          </Button>

          <Link href="/">
            <Button
              variant="ghost"
              className="text-[#4D4D4D] hover:text-[#0066B1] px-8 py-3 text-lg font-semibold transition-all duration-300"
              size="lg"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Inapoi la pagina principala
            </Button>
          </Link>
        </div>

        {/* Support Information */}
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-3xl mx-auto">
          <h3 className="text-xl font-bold text-[#4D4D4D] mb-6">
            Echipa de Asistenta Clienti
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-[#0066B1] rounded-full flex items-center justify-center mx-auto mb-3">
                <Mail className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-[#4D4D4D] mb-2">Email</h4>
              <p className="text-sm text-slate-600">
                <EMAIL>
              </p>
              <p className="text-xs text-slate-500 mt-1">
                Raspuns prioritar in 24 de ore
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-[#0066B1] rounded-full flex items-center justify-center mx-auto mb-3">
                <Phone className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-[#4D4D4D] mb-2">Telefon</h4>
              <p className="text-sm text-slate-600">1-800-BMW-PARTS</p>
              <p className="text-xs text-slate-500 mt-1">
                Luni-Vineri, 08:00 - 17:00
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-[#0066B1] rounded-full flex items-center justify-center mx-auto mb-3">
                <AlertTriangle className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-[#4D4D4D] mb-2">Email de Securitate</h4>
              <p className="text-sm text-slate-600"><EMAIL></p>
              <p className="text-xs text-slate-500 mt-1">
                Raspuns prioritar in 24 de ore
              </p>
            </div>
          </div>
        </div>

        {/* Premium Badge */}
        <div className="mt-12">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#0066B1] to-[#4D4D4D] rounded-full text-white font-semibold shadow-lg">
            <div className="w-2 h-2 bg-white rounded-full mr-3 animate-pulse"></div>
              Piese Premium BMW • Asistenta Clienti • Plata securizata
          </div>
        </div>
      </div>
    </div>
  );
}