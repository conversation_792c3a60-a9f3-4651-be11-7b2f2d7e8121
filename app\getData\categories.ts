"server-only"

import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { MegaMenuCategory, MegaMenuLevel2 } from "@/types/mega-menu"

/**
 * Fetches all categories for the mega-menu with Next.js caching
 * Only returns categories where afisat=true for display in mega-menu
 * Includes full hierarchy: Level1 -> Level2 -> Level3
 */
export async function getMegaMenuCategories(): Promise<MegaMenuCategory[]> {
  'use cache'
  try {
    const categoriesFromDbOrCache = await withRetry(() =>
      prisma.categoryLevel1.findMany({
            where: { 
              afisat: true // Only categories marked for mega-menu display
            },
            select: {
              id: true,
              name: true,
              nameRO: true,
              level2Categories: {
                where: { 
                  afisat: true // Only level2 categories marked for display
                },
                select: {
                  id: true,
                  name: true,
                  nameRO: true,
                  slug: true,
                  level3Categories: {
                    where: { 
                      afisat: true, // Only level3 categories marked for display
                      isActive: true,
                      //deletedAt: null
                    },
                    select: {
                      id: true,
                      name: true,
                      nameRO: true,
                      slug: true,
                      productCount: true
                    },
                    take: 5,
                    orderBy: [
                      { displayOrder: 'asc' },
                      { name: 'asc' }
                    ]
                  }
                },
                orderBy: { name: 'asc' }
              }
            },
            orderBy: { name: 'asc' }
      })
    )

    
    // const categoriesFromDbOrCache = await getCachedData(
    //   cacheKey,
    //   async () => {
    //     return withRetry(() =>
    //       prisma.categoryLevel1.findMany({
    //         where: { 
    //           afisat: true // Only categories marked for mega-menu display
    //         },
    //         select: {
    //           id: true,
    //           name: true,
    //           nameRO: true,
    //           level2Categories: {
    //             where: { 
    //               afisat: true // Only level2 categories marked for display
    //             },
    //             select: {
    //               id: true,
    //               name: true,
    //               nameRO: true,
    //               level3Categories: {
    //                 where: { 
    //                   afisat: true, // Only level3 categories marked for display
    //                   //isActive: true,
    //                   //deletedAt: null
    //                 },
    //                 select: {
    //                   id: true,
    //                   name: true,
    //                   nameRO: true,
    //                   slug: true,
    //                   productCount: true
    //                 },
    //                 take: 5,
    //                 orderBy: [
    //                   { displayOrder: 'asc' },
    //                   { name: 'asc' }
    //                 ]
    //               }
    //             },
    //             orderBy: { name: 'asc' }
    //           }
    //         },
    //         orderBy: { name: 'asc' }
    //       })
    //     )
    //   },
    //   2 // 30 minutes TTL - categories don't change frequently
    // )

    // Filter out level1 categories that have no visible level2 categories
    const filteredCategories = categoriesFromDbOrCache.filter(
      (level1) => level1.level2Categories.length > 0
    )

    return filteredCategories as MegaMenuCategory[]

  } catch (error) {
    logger.error('Error fetching mega-menu categories:', error)
    return []
  }
}

/**
 * Gets a specific category level 2 by name/slug for routing
 * Used to resolve category routes like /category/[category2]
 */
export async function getCategoryLevel2BySlug(slug: string): Promise<MegaMenuLevel2 | null> {
  //const cacheKey = `category-level2:${slug}`
  'use cache'
  try {
    // const category = await getCachedData(
    //   cacheKey,
    //   async () => {
    //     return withRetry(() =>

      const category = await withRetry(() =>
        prisma.categoryLevel2.findFirst({
            where: {
              slug: slug,
              afisat: true
            },
            select: {
              id: true,
              name: true,
              nameRO: true,
              slug: true,
              level3Categories: {
                where: {
                  afisat: true,
                  isActive: true,
                  deletedAt: null
                },
                select: {
                  id: true,
                  name: true,
                  nameRO: true,
                  slug: true,
                  productCount: true
                },
                orderBy: [
                  { displayOrder: 'asc' },
                  { name: 'asc' }
                ]
              }
            }
          })
        )

    return category as MegaMenuLevel2 | null

  } catch (error) {
    logger.error(`Error fetching category level 2 by slug ${slug}:`, error)
    return null
  }
}

/**
 * Gets a specific category level 3 by slug and parent level 2 slug
 * Used to resolve specific category3 selections with unique identification
 */
export async function getCategoryLevel3BySlug(
  level2Slug: string,
  level3Slug: string
): Promise<{ id: string; name: string; nameRO: string | null; slug: string | null } | null> {
  //const cacheKey = `category-level3-slug:${level2Slug}:${level3Slug}`
  'use cache'
  try {
    const category = await withRetry(() =>
          prisma.categoryLevel3.findFirst({
            where: {
              slug: level3Slug,
              afisat: true,
              isActive: true,
              deletedAt: null,
              level2: {
                slug: level2Slug,
                afisat: true,
              }
            },
            select: {
              id: true,
              name: true,
              nameRO: true,
              slug: true
            }
          })
        )

    return category

  } catch (error) {
    logger.error(`Error fetching category level 3 by slug ${level2Slug}/${level3Slug}:`, error)
    return null
  }
}

/**
 * Gets all category level 3 IDs under a specific level 2 category
 * Used for filtering products by category2
 */
export async function getCategoryLevel3IdsByLevel2Slug(level2Slug: string): Promise<string[]> {
  'use cache'
  
  try {
    const level2Category = await withRetry(() =>
      prisma.categoryLevel2.findFirst({
        where: {
          slug: level2Slug,
          afisat: true
        },
        select: {
          level3Categories: {
            where: {
              afisat: true,
              isActive: true,
              deletedAt: null
            },
            select: { id: true }
          }
        }
      })
    )

    return level2Category?.level3Categories.map(cat => cat.id) || []

  } catch (error) {
    logger.error(`Error fetching category level 3 IDs for level 2 slug ${level2Slug}:`, error)
    return []
  }
}
// export async function getCategoryLevel3IdsByLevel2Slug(level2Slug: string): Promise<string[]> {
//   const cacheKey = `category-level3-ids:${level2Slug}`

//   try {
//     const categoryIds = await getCachedData(
//       cacheKey,
//       async () => {
//         const level2Category = await withRetry(() =>
//           prisma.categoryLevel2.findFirst({
//             where: {
//               slug: level2Slug,
//               afisat: true
//             },
//             select: {
//               level3Categories: {
//                 where: {
//                   afisat: true,
//                   isActive: true,
//                   deletedAt: null
//                 },
//                 select: { id: true }
//               }
//             }
//           })
//         )

//         return level2Category?.level3Categories.map(cat => cat.id) || []
//       },
//       60 * 15 // 15 minutes TTL
//     )

//     return categoryIds

//   } catch (error) {
//     logger.error(`Error fetching category level 3 IDs for level 2 slug ${level2Slug}:`, error)
//     return []
//   }
// }

/**
 * Debug function to check and populate missing slugs
 * Run this once to ensure all categories have slugs
 */
export async function debugAndPopulateSlugs() {
  try {
    // Check CategoryLevel2 slugs
    const level2WithoutSlugs = await withRetry(() =>
      prisma.categoryLevel2.findMany({
        where: {
          OR: [
            { slug: null },
            { slug: "" }
          ]
        },
        select: { id: true, name: true, nameRO: true, slug: true }
      })
    )

    logger.info(`[debugAndPopulateSlugs] CategoryLevel2 without slugs:`, { count: level2WithoutSlugs.length, categories: level2WithoutSlugs })

    // Check CategoryLevel3 slugs
    const level3WithoutSlugs = await withRetry(() =>
      prisma.categoryLevel3.findMany({
        where: {
          OR: [
            { slug: null },
            { slug: "" }
          ]
        },
        select: { id: true, name: true, nameRO: true, slug: true }
      })
    )

    logger.info(`[debugAndPopulateSlugs] CategoryLevel3 without slugs:`, { count: level3WithoutSlugs.length, categories: level3WithoutSlugs })

    return {
      level2WithoutSlugs: level2WithoutSlugs.length,
      level3WithoutSlugs: level3WithoutSlugs.length
    }

  } catch (error) {
    logger.error('Error in debugAndPopulateSlugs:', error)
    return null
  }
}

// Re-export utility functions for server-side use
export { generateCategoryUrl } from "@/lib/category-utils"
