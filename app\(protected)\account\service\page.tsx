"server-only"

import { getCurrentDbUser } from "@/lib/auth";
import { getUserServiceRequests, getServiceStatistics, getProductsForService } from "@/app/getData/services";
import { getActiveShowrooms, getUserShippingAddresses } from "@/app/getData/addresses";
import { redirect } from "next/navigation";
import { logger } from "@/lib/logger";
import { serviceFiltersSchema } from "@/lib/zod";
import { ServiceFilters } from "@/types/services";
import ServicePage from "@/app/components/account/service/ServicePage";
import { ServiceErrorBoundary, ServiceError } from "@/app/components/account/service/ServiceErrorBoundary";

interface ServiceRouteProps {
  searchParams: Promise<{
    page?: string;
    status?: string;
    type?: string;
    search?: string;
    limit?: string;
    dateFrom?: string;
    dateTo?: string;
  }>;
}

export default async function ServiceRoute({ searchParams }: ServiceRouteProps) {

  // Get current user
  const user = await getCurrentDbUser();
  if (!user) {
    return redirect("/sign-in");
  }

  const params = await searchParams;
  const validationResult = serviceFiltersSchema.safeParse(params);

  if (!validationResult.success) {
    return redirect("/account/service"); // Redirect to a clean state
  }

  const validatedFilters: ServiceFilters = validationResult.data;

  try {
    // Fetch service data, statistics, available products, addresses, and showrooms in parallel
    const [serviceData, statistics, availableProducts, availableAddresses, showrooms] = await Promise.all([
      getUserServiceRequests(user.id, validatedFilters),
      getServiceStatistics(user.id),
      getProductsForService(user.id),
      getUserShippingAddresses(user.id),
      getActiveShowrooms(),
    ]);

    return (
      <ServiceErrorBoundary>
        <ServicePage
          initialServiceRequests={serviceData.serviceRequests}
          pagination={serviceData.pagination}
          filters={validatedFilters}
          statistics={statistics}
          availableProducts={availableProducts}
          availableAddresses={availableAddresses}
          showrooms={showrooms}
        />
      </ServiceErrorBoundary>
    );

  } catch (error) {
    logger.error(`[ServiceRoute] Error fetching service data for user ${user.id}:`, error);

    // Return error state
    return (
      <ServiceError
        title="Eroare la încărcarea serviciilor"
        message="Nu am putut încărca cererile de service. Te rugăm să încerci din nou."
        //onRetry={() => window.location.reload()}
      />
    );
  }
}