import AccountInactivePage from "@/app/components/account/suspend-inactive/InactivePage";
import { prisma, withRetry } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";

export default async function AccountInactiveRoute() {

  const { userId } = await auth();
  if (!userId) {
    redirect("/sign-in");
  }

  const checkInactive = async () => {
    const user = await withRetry(() =>
      prisma.user.findUnique({
        where: { externalId: userId },
        select: { isActive: true }
      })
    );

    if (user) {
      return user.isActive;
    }
    return null;
  };

  const isActive = await checkInactive();

  if(isActive){
    redirect("/sign-in");
  }

  return <AccountInactivePage />;
}


// import { SignOutButton } from "@clerk/nextjs";
// import { Button } from "@/components/ui/button";
// import { getCurrentDbUser } from "@/lib/auth";
// import { redirect } from "next/navigation";

// export default async function AccountInactivePage() {
//     const user = await getCurrentDbUser()
  
//     if(!user){
//       redirect("sign-in")
//     }
//   return (
//     <div className="container mx-auto py-16 text-center">
//       <div className="max-w-md mx-auto p-8 rounded-lg shadow-md">
//         <h1 className="text-2xl font-bold text-amber-600 mb-4">Account Inactive</h1>
        
//         <p className="mb-6 text-gray-700">
//           Your account is currently inactive. This may be because your account
//           is new and pending approval, or it has been deactivated.
//         </p>
        
//         <p className="mb-8 text-gray-700">
//           If you believe this is an error, please contact our support team at 
//           <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
//             <EMAIL>
//           </a>
//         </p>
        
//         <SignOutButton>
//           <Button variant="outline" className="w-full">
//             Sign Out
//           </Button>
//         </SignOutButton>
//       </div>
//     </div>
//   );
// }