import type { 
  ReturnStatus as PrismaReturnStatus,
  ReturnReason as PrismaReturnReason,
  ReturnItemReason as PrismaReturnItemReason,
  RefundMethod as PrismaRefundMethod,
  ItemCondition as PrismaItemCondition,
  InspectionResult as PrismaInspectionResult
} from "@/generated/prisma";
import { Decimal } from "@/generated/prisma/runtime/library";

// Return Item Interface for UI
export interface ReturnItem {
  id: string;
  quantity: number;
  reason: PrismaReturnItemReason;
  condition: PrismaItemCondition;
  description?: string;
  isReceived: boolean;
  isInspected: boolean;
  inspectionNotes?: string;
  inspectionResult?: PrismaInspectionResult;
  orderItem: {
    id: string;
    quantity: number;
    price: number;
    product: {
      id: string;
      Material_Number: string;
      Description_Local: string;
      ImageUrl: string[];
    };
  };
}

// Return Interface for UI (updated for new schema)
export interface Return {
  id: string;
  returnNumber: string;
  status: PrismaReturnStatus;

  // Order information
  orderId: string;
  orderNumber: string;

  // Action information (from OrderPostPurchaseAction)
  actionNumber?: string;

  // Shipping information
  method: 'curier' | 'intern' | 'showroom';
  address?: {
    id: string;
    fullName: string;
    address: string;
    city: string;
    county: string;
    phoneNumber: string;
    notes?: string;
  };
  showroom?: {
    id: string;
    code: string;
    name: string;
    address1: string;
    address2?: string;
    city: string;
    county: string;
    phone: string;
    email?: string;
    program?: string;
  };

  // Approval details
  isApproved?: boolean;
  approvedBy?: string;
  approvedAt?: string;
  rejectionReason?: string;

  // Financial details
  refundAmount?: number;
  refundMethod?: PrismaRefundMethod;
  refundedAt?: string;
  refundReference?: string;

  // Logistics
  returnShippingLabel?: string;
  receivedAt?: string;
  inspectedAt?: string;
  createdAt: string;
  updatedAt: string;
  order: {
    id: string;
    orderNumber: string;
    placedAt: string;
  };
  returnItems: ReturnItem[];
}

// Raw Return from Prisma (for data fetching - updated for new schema)
export interface RawReturnFromPrisma {
  id: string;
  returnNumber: string;
  status: PrismaReturnStatus;
  orderId: string;
  // Shipping information
  method: 'curier' | 'intern' | 'showroom';
  address?: {
    id: string;
    fullName: string;
    address: string;
    city: string;
    county: string;
    phoneNumber: string;
    notes?: string | null;
  } | null;
  showroom?: {
    id: string;
    code: string;
    name: string;
    address1: string;
    address2?: string | null;
    city: string;
    county: string;
    phone: string;
    email?: string | null;
    program?: string | null;
  } | null;
  isApproved?: boolean | null;
  approvedBy?: string | null;
  approvedAt?: Date | null;
  rejectionReason?: string | null;
  refundAmount?: Decimal | null;
  refundMethod?: PrismaRefundMethod | null;
  refundedAt?: Date | null;
  refundReference?: string | null;
  returnShippingLabel?: string | null;
  receivedAt?: Date | null;
  inspectedAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
  order: {
    id: string;
    orderNumber: string;
    placedAt: Date;
  };
  action: {
    id: string;
    actionNumber: string;
  } [];
  returnItems: RawReturnItemFromPrisma[];
}

// Raw Return Item from Prisma (for data fetching)
export interface RawReturnItemFromPrisma {
  id: string;
  quantity: number;
  reason: PrismaReturnItemReason;
  condition: PrismaItemCondition;
  description?: string | null;
  isReceived: boolean;
  isInspected: boolean;
  inspectionNotes?: string | null;
  inspectionResult?: PrismaInspectionResult | null;
  orderItem: {
    id: string;
    quantity: number;
    price: Decimal;
    product: {
      id: string;
      Material_Number: string;
      Description_Local: string | null;
      ImageUrl: string[];
    };
  };
}

// Return List Response Interface
export interface ReturnsResponse {
  returns: Return[];
  pagination: {
    total: number;
    pages: number;
    currentPage: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Return Filters Interface
export interface ReturnFilters {
  status?: PrismaReturnStatus | 'all';
  search?: string;
  page?: number;
  limit?: number;
  dateFrom?: string;
  dateTo?: string;
}

// Order Item for Return Selection
export interface OrderItemForReturn {
  id: string;
  quantity: number;
  price: number;
  product: {
    id: string;
    Material_Number: string;
    Description_Local: string;
    ImageUrl: string[];
  };
  order: {
    id: string;
    orderNumber: string;
    placedAt: string;
  };
  // Track how many items are already being returned
  availableQuantity: number;
}

// New Return Request Interface
export interface CreateReturnRequest {
  orderId: string;
  reason: PrismaReturnReason;
  additionalNotes?: string;
  items: {
    orderItemId: string;
    quantity: number;
    reason: PrismaReturnItemReason;
    description?: string;
  }[];
  shippingAddress: {
    fullName: string;
    address: string;
    city: string;
    county: string;
    phoneNumber: string;
    notes?: string;
  };
}

// Return Status History Interface
export interface ReturnStatusHistory {
  id: string;
  status: PrismaReturnStatus;
  notes?: string;
  createdAt: string;
  createdBy?: string;
}

// Return Timeline Event Interface
export interface ReturnTimelineEvent {
  date: string;
  status: PrismaReturnStatus;
  title: string;
  description?: string;
  isCompleted: boolean;
  isCurrent: boolean;
}

// Return Statistics Interface
export interface ReturnStatistics {
  total: number;
  pending: number;
  approved: number;
  completed: number;
  rejected: number;
}

// Shipping Address for Returns
export interface ReturnShippingAddress {
  fullName: string;
  address: string;
  city: string;
  county: string;
  phoneNumber: string;
  notes?: string;
}

// Return Form Data Interface
export interface ReturnFormData {
  selectedItems: {
    orderItemId: string;
    quantity: number;
    reason: PrismaReturnItemReason;
    description?: string;
  }[];
  returnReason: PrismaReturnReason;
  additionalNotes?: string;
  shippingAddress: ReturnShippingAddress;
}

// Return Action Result Interface
export interface ReturnActionResult {
  success: boolean;
  error?: string;
  returnId?: string;
  returnNumber?: string;
}

// Return Status Display Interface
export interface ReturnStatusDisplay {
  status: PrismaReturnStatus;
  label: string;
  color: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning';
  description: string;
}

// Return Reason Display Interface
export interface ReturnReasonDisplay {
  reason: PrismaReturnReason | PrismaReturnItemReason;
  label: string;
  description: string;
}
export const ReturnReason = {
  wrongItem: 'wrongItem',
  defective: 'defective',
  damaged: 'damaged',
  notAsDescribed: 'notAsDescribed',
  noLongerWanted: 'noLongerWanted',
  other: 'other'
} as const

export type ReturnReason = typeof ReturnReason[keyof typeof ReturnReason]

export const ReturnItemReason = {
  wrongItem: 'wrongItem',
  defective: 'defective',
  damaged: 'damaged',
  notAsDescribed: 'notAsDescribed',
  noLongerWanted: 'noLongerWanted',
  other: 'other'
} as const

export type ReturnItemReason = typeof ReturnItemReason[keyof typeof ReturnItemReason]

export const ReturnStatus = {
  requested: 'requested',
  approved: 'approved',
  rejected: 'rejected',
  awaitingReceipt: 'awaitingReceipt',
  received: 'received',
  inspected: 'inspected',
  refundIssued: 'refundIssued',
  completed: 'completed',
  cancelled: 'cancelled'
} as const

export type ReturnStatus = typeof ReturnStatus[keyof typeof ReturnStatus]