"server-only"

import { logger } from "@/lib/logger";
import { redis } from "@/lib/redis";
import { cuidSchema } from "@/lib/zod";
import { Cart } from "@/types/cart";
import { cache } from 'react'
import { prisma } from "@/lib/db";
import { getPricesFor4thBatch } from "@/lib/mssql/query";
import { GetPrice4LvlBatch } from "@/types/mssql";
import { toSafeNumber } from "@/lib/utils";

export const getCart = cache(async (userIdDB: string): Promise<Cart> => {
  if (!userIdDB) {
    logger.warn(`[getCart] No userId provided`);
    return { items: [], order: { notes: "" } };
  }

  const userIdParsed = cuidSchema.safeParse(userIdDB);

  if (!userIdParsed.success) {
    logger.error("[getCart] Invalid user ID provided:", userIdParsed.error.format());
    return { items: [], order: { notes: "" } };
  }

  if (!redis) {
    logger.error("[getCart] Redis connection not available");
    return { items: [], order: { notes: "" } };
  }

  const userId = userIdParsed.data

  try {
    const cacheKey = `cart-${userId}`;
    const cart: Cart | null = await redis.get(cacheKey);

    if (!cart) {
      return { items: [], order: { notes: "" } };
    }

    return cart;
  } catch (error) {
    logger.error(`[getCart] Error fetching cart: ${error}`);
    return { items: [], order: { notes: "" } };
  }
})

export const getCartWithFreshPrices = async (
  userIdDB: string,
  has4thLevel: boolean,
  userAM?: string
): Promise<Cart> => {
  if (!userIdDB) {
    logger.warn(`[getCartWithFreshPrices] No userId provided`);
    return { items: [], order: { notes: "" } };
  }

  const userIdParsed = cuidSchema.safeParse(userIdDB);

  if (!userIdParsed.success) {
    logger.error("[getCartWithFreshPrices] Invalid user ID provided:", userIdParsed.error.format());
    return { items: [], order: { notes: "" } };
  }

  if (!redis) {
    logger.error("[getCartWithFreshPrices] Redis connection not available");
    return { items: [], order: { notes: "" } };
  }

  const userId = userIdParsed.data

  try {
    const cacheKey = `cart-${userId}`;
    const cart: Cart | null = await redis.get(cacheKey);

    if (!cart || cart.items.length === 0) {
      return { items: [], order: { notes: "" } };
    }

    // Get fresh product data from database
    const productIds = cart.items.map(item => item.id);
    const freshProducts = await prisma.product.findMany({
      where: {
        id: { in: productIds }
      },
      select: {
        id: true,
        Material_Number: true,
        FinalPrice: true,
        isActive: true,
      }
    });

    // Get 4th level pricing if applicable
    let price4thBatch: GetPrice4LvlBatch[] = [];
    if (has4thLevel && userAM) {
      const materialNumbers = freshProducts.map(p => p.Material_Number);
      //price4thBatch = [{ itemno: "12120032137", pret: 111 }, { itemno: "63136921512", pret: 456 }];
      price4thBatch = await getPricesFor4thBatch(materialNumbers, userAM);
      logger.info(`[getCartWithFreshPrices] Retrieved 4th level pricing for ${price4thBatch.length} products`);
    }

    // Create a map for easy 4th level price lookup
    const price4thMap = new Map(price4thBatch.map(p => [p.itemno, p.pret]));

    // Update cart items with fresh prices
    const updatedItems = cart.items.map(cartItem => {
      const freshProduct = freshProducts.find(p => p.id === cartItem.id);

      if (!freshProduct) {
        logger.warn(`[getCartWithFreshPrices] Product not found in database: ${cartItem.id}`);
        return cartItem; // Keep original item if product not found
      }

      if (!freshProduct.isActive) {
        logger.warn(`[getCartWithFreshPrices] Product is inactive: ${cartItem.Material_Number}`);
      }

      // Use 4th level price if available, otherwise use fresh product price
      const fourthLevelPrice = price4thMap.get(freshProduct.Material_Number);
      const finalPrice = fourthLevelPrice ?? toSafeNumber(freshProduct.FinalPrice) ?? 0;

      if (fourthLevelPrice !== undefined) {
        logger.info(`[getCartWithFreshPrices] Using 4th level price for ${freshProduct.Material_Number}: ${fourthLevelPrice} (original: ${freshProduct.FinalPrice})`);
      }

      return {
        ...cartItem,
        FinalPrice: finalPrice,
      };
    });

    const updatedCart = {
      ...cart,
      items: updatedItems,
    };

    // Update the cart in Redis with fresh prices so navbar cart preview shows correct prices
    try {
      await redis.set(cacheKey, updatedCart);
      logger.info(`[getCartWithFreshPrices] Updated cart in Redis with fresh prices for user ${userId}`);
    } catch (error) {
      logger.error(`[getCartWithFreshPrices] Failed to update cart in Redis: ${error}`);
      // Don't fail the entire operation if Redis update fails
    }

    return updatedCart;

  } catch (error) {
    logger.error(`[getCartWithFreshPrices] Error fetching cart with fresh prices: ${error}`);
    return { items: [], order: { notes: "" } };
  }
}