"server-only"

import { cache } from "react";
import { prisma, withRetry } from "@/lib/db";
import { logger } from "@/lib/logger";
import {
  ServiceRequest,
  ServiceRequestsResponse,
  ServiceFilters,
  ServiceStatistics,
  RawServiceRequestFromPrisma,
  serviceRequestSelect,
  ServiceStatusHistory,
  ProductForService
} from "@/types/services";
import { ServiceStatus } from "@/generated/prisma";
import { cuidSchema } from "@/lib/zod";

/**
 * Transform raw service request data from Prisma to UI format (following return pattern)
 */
function transformServiceRequestData(rawServiceRequest: RawServiceRequestFromPrisma): ServiceRequest {
  return {
    id: rawServiceRequest.id,
    serviceNumber: rawServiceRequest.serviceNumber,
    userId: rawServiceRequest.userId,

    // Data from OrderPostPurchaseAction (if available)
    actionNumber: rawServiceRequest.action?.actionNumber,
    orderItemId: rawServiceRequest.action?.orderItemId,
    orderId: rawServiceRequest.action?.orderItem?.orderId,
    orderNumber: rawServiceRequest.action?.orderItem?.order?.orderNumber,
    productName: rawServiceRequest.action?.orderItem?.product?.Description_Local || 'Produs necunoscut',
    productCode: rawServiceRequest.action?.orderItem?.product?.Material_Number,
    productImage: rawServiceRequest.action?.orderItem?.product?.ImageUrl,

    // Issue details
    issueType: rawServiceRequest.issueType,
    description: rawServiceRequest.description,

    // Shipping information
    method: rawServiceRequest.method,
    addressId: rawServiceRequest.addressId,
    address: rawServiceRequest.address ? {
      id: rawServiceRequest.address.id,
      fullName: rawServiceRequest.address.fullName,
      address: rawServiceRequest.address.address,
      city: rawServiceRequest.address.city,
      county: rawServiceRequest.address.county,
      phoneNumber: rawServiceRequest.address.phoneNumber,
      notes: rawServiceRequest.address.notes || undefined,
    } : null,
    showroomId: rawServiceRequest.showroomId,
    showroom: rawServiceRequest.showroom ? {
      id: rawServiceRequest.showroom.id,
      code: rawServiceRequest.showroom.code,
      name: rawServiceRequest.showroom.name,
      address1: rawServiceRequest.showroom.address1,
      address2: rawServiceRequest.showroom.address2 || undefined,
      city: rawServiceRequest.showroom.city,
      county: rawServiceRequest.showroom.county,
      phone: rawServiceRequest.showroom.phone,
      email: rawServiceRequest.showroom.email || undefined,
      program: rawServiceRequest.showroom.program || undefined,
    } : null,

    // Status tracking
    status: rawServiceRequest.status,
    resolution: rawServiceRequest.resolution || undefined,
    resolutionNotes: rawServiceRequest.resolutionNotes || undefined,
    resolvedAt: rawServiceRequest.resolvedAt?.toISOString(),

    // Audit fields
    createdAt: rawServiceRequest.createdAt.toISOString(),
    updatedAt: rawServiceRequest.updatedAt.toISOString(),

    // Status history
    statusHistory: rawServiceRequest.statusHistory.map(transformStatusHistoryData),
  };
}

// Remove service item transformation as we're following return pattern

/**
 * Transform raw status history data from Prisma to UI format
 */
function transformStatusHistoryData(rawHistory: {
  id: string;
  previousStatus: ServiceStatus | null;
  newStatus: ServiceStatus;
  notes: string | null;
  changedBy: string;
  changedAt: Date;
  emailSent: boolean;
}): ServiceStatusHistory {
  return {
    id: rawHistory.id,
    serviceRequestId: '', // Will be set by parent
    previousStatus: rawHistory.previousStatus || undefined,
    newStatus: rawHistory.newStatus,
    notes: rawHistory.notes || undefined,
    changedBy: rawHistory.changedBy,
    changedAt: rawHistory.changedAt.toISOString(),
    emailSent: rawHistory.emailSent,
  };
}

/**
 * Fetches service requests for a user with filtering and pagination (following return pattern)
 */
export const getUserServiceRequests = cache(async (
  userId: string,
  filters: ServiceFilters
): Promise<ServiceRequestsResponse> => {
  try {
    const userIdValidation = cuidSchema.safeParse(userId);
    if (!userIdValidation.success) {
      logger.error(`[getUserServiceRequests] Invalid userId: ${userId}`);
      throw new Error('Invalid user ID');
    }

    const { page = 1, limit = 10, status, search, dateFrom, dateTo } = filters;
    const skip = (page - 1) * limit;

    // Build where clause
    const where: {
      userId: string;
      status?: ServiceStatus;
      OR?: Array<{
        serviceNumber?: { contains: string; mode: 'insensitive' };
        order?: { orderNumber: { contains: string; mode: 'insensitive' } };
        product?: {
          Material_Number?: { contains: string; mode: 'insensitive' };
          Description_Local?: { contains: string; mode: 'insensitive' };
        };
      }>;
      createdAt?: {
        gte?: Date;
        lte?: Date;
      };
    } = {
      userId,
    };

    // Status filter
    if (status && status !== 'all') {
      where.status = status;
    }

    // Search filter
    if (search && search.trim()) {
      where.OR = [
        { serviceNumber: { contains: search, mode: 'insensitive' } },
        { order: { orderNumber: { contains: search, mode: 'insensitive' } } },
        { product: { Material_Number: { contains: search, mode: 'insensitive' } } },
        { product: { Description_Local: { contains: search, mode: 'insensitive' } } },
      ];
    }

    // Date range filter
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo);
      }
    }

    // Execute queries in parallel
    const [rawServiceRequests, total] = await Promise.all([
      withRetry(() =>
        prisma.serviceRequest.findMany({
          where,
          select: serviceRequestSelect,
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        })
      ),
      withRetry(() =>
        prisma.serviceRequest.count({ where })
      ),
    ]);

    const serviceRequests = rawServiceRequests.map(transformServiceRequestData);

    return {
      serviceRequests,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  } catch (error) {
    logger.error(`[getUserServiceRequests] Error fetching service requests for user ${userId}:`, error);
    throw new Error('Failed to fetch service requests');
  }
});

/**
 * Fetches a single service request by ID
 */
export const getServiceRequestById = cache(async (
  serviceRequestId: string,
  userId: string
): Promise<ServiceRequest | null> => {
  try {
    const rawServiceRequest = await withRetry(() =>
      prisma.serviceRequest.findFirst({
        where: {
          id: serviceRequestId,
          userId, // Ensure user can only access their own service requests
        },
        select: serviceRequestSelect,
      })
    );

    if (!rawServiceRequest) {
      logger.warn(`[getServiceRequestById] Service request not found: ${serviceRequestId} for user: ${userId}`);
      return null;
    }

    return transformServiceRequestData(rawServiceRequest);
  } catch (error) {
    logger.error(`[getServiceRequestById] Error fetching service request ${serviceRequestId}:`, error);
    return null;
  }
});

/**
 * Fetches service statistics for a user (following return pattern)
 */
export const getServiceStatistics = cache(async (userId: string): Promise<ServiceStatistics> => {
  try {
    const userIdValidation = cuidSchema.safeParse(userId);
    if (!userIdValidation.success) {
      logger.error(`[getServiceStatistics] Invalid userId: ${userId}`);
      return {
        total: 0,
        requested: 0,
        approved: 0,
        rejected: 0,
        awaitingReceipt: 0,
        received: 0,
        processing: 0,
        completed: 0,
        cancelled: 0,
      };
    }

    const stats = await withRetry(() =>
      prisma.serviceRequest.groupBy({
        by: ['status'],
        where: { userId },
        _count: { status: true },
      })
    );

    const result: ServiceStatistics = {
      total: 0,
      requested: 0,
      approved: 0,
      rejected: 0,
      awaitingReceipt: 0,
      received: 0,
      processing: 0,
      completed: 0,
      cancelled: 0,
    };

    stats.forEach((stat) => {
      const count = stat._count.status;
      result.total += count;

      switch (stat.status) {
        case ServiceStatus.requested:
          result.requested = count;
          break;
        case ServiceStatus.scheduled:
          result.approved = count; // Map scheduled to approved for UI
          break;
        case ServiceStatus.inProgress:
        case ServiceStatus.diagnosisComplete:
        case ServiceStatus.awaitingParts:
        case ServiceStatus.awaitingApproval:
          result.processing += count; // Group all in-progress statuses
          break;
        case ServiceStatus.completed:
        case ServiceStatus.delivered:
          result.completed += count;
          break;
        case ServiceStatus.cancelled:
          result.cancelled = count;
          break;
      }
    });

    return result;
  } catch (error) {
    logger.error(`[getServiceStatistics] Error fetching service statistics for user ${userId}:`, error);
    return {
      total: 0,
      requested: 0,
      approved: 0,
      rejected: 0,
      awaitingReceipt: 0,
      received: 0,
      processing: 0,
      completed: 0,
      cancelled: 0,
    };
  }
});

/**
 * Get products available for service requests (from user's delivered orders)
 * Production-ready implementation
 */
export const getProductsForService = cache(async (userId: string): Promise<ProductForService[]> => {
  try {
    const userIdValidation = cuidSchema.safeParse(userId);
    if (!userIdValidation.success) {
      logger.error(`[getProductsForService] Invalid userId: ${userId}`);
      return [];
    }

    // First, get existing active actions to exclude
    const existingActions = await prisma.orderPostPurchaseAction.findMany({
        where: {
          userId,
          status: 'ACTIVE',
        },
        select: {
          orderItemId: true,
        },
      })
    

    const excludedOrderItemIds = existingActions.map(action => action.orderItemId);
    //logger.info(`[getProductsForService] Excluding ${excludedOrderItemIds.length} items with active actions`);

    // Get order items from delivered orders that are serviceable and don't have active actions
    const orderItems = await prisma.orderItem.findMany({
        where: {
          order: {
            userId,
            orderStatus: 'completa', // Only completed/delivered orders
          },
          product: {
            //isServiceable: true, // Only serviceable products
          },
          id: {
            notIn: excludedOrderItemIds, // Exclude items with active actions
          },
        },
        take: 50, // Limit to 50 products to prevent performance issues
        select: {
          id: true,
          productId: true,
          quantity: true,
          orderId: true,
          product: {
            select: {
              id: true,
              Material_Number: true,
              Description_Local: true,
              ImageUrl: true,
              isServiceable: true,
            },
          },
          order: {
            select: {
              id: true,
              orderNumber: true,
              createdAt: true,
            },
          },
        },
        orderBy: {
          order: {
            createdAt: 'desc', // Most recent orders first
          },
        },
      })
    ;

    // Transform to ProductForService interface
    const serviceableProducts: ProductForService[] = orderItems.map(item => ({
      orderItemId: item.id,
      productId: item.product.id,
      materialNumber: item.product.Material_Number,
      description: item.product.Description_Local || 'Produs necunoscut',
      image: item.product.ImageUrl || [],
      orderId: item.order.id,
      orderNumber: item.order.orderNumber,
      orderDate: item.order.createdAt.toISOString(),
      quantity: item.quantity,
      isServiceable: item.product.isServiceable,
    }));

    logger.info(`[getProductsForService] Found ${serviceableProducts.length} serviceable products for user ${userId}`);

    return serviceableProducts;
  } catch (error) {
    logger.error(`[getProductsForService] Error fetching products for service for user ${userId}:`, error);
    return [];
  }
});

/**
 * Get user addresses available for service requests
 * Production-ready implementation
 */
export const getAddressesForService = cache(async (userId: string): Promise<Array<{
  id: string;
  address: string;
  city: string;
  county: string;
  phoneNumber: string;
}>> => {
  try {
    const userIdValidation = cuidSchema.safeParse(userId);
    if (!userIdValidation.success) {
      logger.error(`[getAddressesForService] Invalid userId: ${userId}`);
      return [];
    }

    // Get user's shipping addresses
    const addresses = await withRetry(() =>
      prisma.shippingAddress.findMany({
        where: {
          userId,
        },
        select: {
          id: true,
          address: true,
          city: true,
          county: true,
          phoneNumber: true,
        },
        orderBy: {
          createdAt: 'desc', // Most recent addresses first
        },
      })
    );

    logger.info(`[getAddressesForService] Found ${addresses.length} addresses for user ${userId}`);

    return addresses;
  } catch (error) {
    logger.error(`[getAddressesForService] Error fetching addresses for service for user ${userId}:`, error);
    return [];
  }
});
