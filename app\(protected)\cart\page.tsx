"server-only"

import { getCurrentDbUser } from "@/lib/auth";
import { ShoppingCart } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { redirect } from "next/navigation";
import { getCartWithFreshPrices } from "@/app/getData/cart";
import { getUserShippingAddresses, getActiveShowrooms } from "@/app/getData/addresses";
import CartPage from "@/app/components/cart/CartPage";
import { logger } from "@/lib/logger";

export default async function CartRoute() {
    try{
    const user = await getCurrentDbUser()

    if(!user) redirect("/sign-in")

    const has4th =
        user.role.includes("fourLvlAdminAB") ||
        user.role.includes("fourLvlInregistratAB");

    const [cart, shippingAddresses, showrooms] = await Promise.all([
      getCartWithFreshPrices(user.id, has4th, user.userAM || ""),
      getUserShippingAddresses(user.id),
      getActiveShowrooms()
    ]);

    if (!cart || cart.items.length === 0 || !cart.items) return (
        <div className="flex max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8 flex-col items-center justify-center rounded-lg border border-dashed text-center">
            <div className="flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
                <ShoppingCart className="w-10 h-10 text-primary" />
            </div>
            <h2 className="mt-6 text-xl font-semibold">Cosul tau este gol</h2>
            <p className="mb-8 mt-2 text-center text-sm leading-6 text-muted-foreground max-w-sm mx-auto">
                Nu ai adaugat inca niciun produs in cos. Exploreaza produsele noastre si adauga-le in cos.
            </p>
            <Button asChild>
                <Link href="/">Vezi produsele</Link>
            </Button>
        </div>
    )

    return (
        <>
            <CartPage has4th={has4th} cart={cart} shippingAddresses={shippingAddresses} showrooms={showrooms} />
        </>
    )
    }catch(e){
        logger.error(`[CartRoute] Error fetching cart: ${e}`);
        return (
            <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8">
                <div className="text-center py-12">
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                        Nu s-au putut incarca produsele din cos.
                    </h2>
                    <p className="text-gray-600 mb-6 max-w-md mx-auto">
                        A aparut o problema la incarcarea produselor din cos. Reincarca pagina sau incearca din nou mai tarziu.
                    </p>
                </div>
            </div>
        )
    }
}
