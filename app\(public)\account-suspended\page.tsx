import AccountSuspendedPage from "@/app/components/account/suspend-inactive/SuspendedPage";
import { prisma, withRetry } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";

export default async function AccountSuspendedRoute() {

  const { userId } = await auth();
  if (!userId) {
    redirect("/sign-in");
  }

  const getLockoutUntil = async () => {
    const user = await withRetry(() =>
      prisma.user.findUnique({
        where: { externalId: userId },
        select: { lockoutUntil: true }
      })
    );

    if (user) {
      return user.lockoutUntil;
    }
    return null;
  };

  const lockoutUntil = await getLockoutUntil();

  if(!lockoutUntil){
    redirect("/sign-in");
  }

  return <AccountSuspendedPage lockoutUntil={lockoutUntil} />;
}
