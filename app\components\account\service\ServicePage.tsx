"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Eye,
  Plus,
  ChevronLeft,
  ChevronRight,
  Search,
  Wrench,
  Calendar,
  Clock,
  Package,
} from "lucide-react";
import { useState, useTransition, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import ServiceRequestDetails from "./ServiceRequestDetails";
import NewServiceRequestDialog from "./NewServiceRequestDialog";
import { ServiceRequest, ServiceFilters, ServiceStatistics, ProductForService, ServiceStatus } from "@/types/services";
import { ShowroomData, ShippingAddress } from "@/types/addresses";
import { formatDate } from "@/lib/order-utils";
import { ServiceStatusBadge, getServiceStatusDisplay } from "./ServiceStatusBadge";

interface ServicePageProps {
  initialServiceRequests: ServiceRequest[];
  pagination: {
    total: number;
    pages: number;
    currentPage: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: ServiceFilters;
  statistics: ServiceStatistics;
  availableProducts: ProductForService[];
  availableAddresses: ShippingAddress[];
  showrooms: ShowroomData[];
}

export default function ServicePage({
  initialServiceRequests,
  pagination,
  filters,
  statistics,
  availableProducts,
  availableAddresses,
  showrooms,
}: ServicePageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const [selectedServiceRequest, setSelectedServiceRequest] = useState<ServiceRequest | null>(null);
  const [showNewServiceDialog, setShowNewServiceDialog] = useState(false);

  // Filter state
  const [searchTerm, setSearchTerm] = useState(filters.search || "");
  const [statusFilter, setStatusFilter] = useState<ServiceFilters['status']>(filters.status || "all");

  // In ServicePage.tsx

const updateFilters = useCallback((newFilters: Partial<ServiceFilters>) => {
  // Always start with the current URL's parameters to preserve existing ones.
  const params = new URLSearchParams(searchParams.toString());
  
  Object.entries(newFilters).forEach(([key, value]) => {
    const stringValue = String(value);

    // ✅ ROBUST LOGIC:
    // If a value is provided (even a "default" like 'all'), SET it.
    // Only DELETE the parameter if the new value is explicitly empty/null/undefined.
    if (value !== undefined && value !== null && stringValue !== '') {
      params.set(key, stringValue);
    } else {
      params.delete(key);
    }
  });

  // When a primary filter (like search or status) changes, always reset to page 1.
  const hasPrimaryFilterChanged = Object.keys(newFilters).some(k => k === 'search' || k === 'status');
  if (hasPrimaryFilterChanged) {
    params.delete('page');
  }

  const newUrlPath = `/account/service?${params.toString()}`;
  const currentUrlPath = `/account/service?${searchParams.toString()}`;

  // ✅ SAFETY CHECK: Prevent pushing the exact same URL again.
  if (newUrlPath === currentUrlPath) {
    return;
  }

  startTransition(() => {
    router.push(newUrlPath);
  });
}, [router, searchParams]);

  // const updateFilters = useCallback((newFilters: Partial<ServiceFilters>) => {
  //   const params = new URLSearchParams(searchParams);
    
  //   Object.entries(newFilters).forEach(([key, value]) => {
  //     if (value && value !== "" && value !== "all") {
  //       params.set(key, value.toString());
  //     } else {
  //       params.delete(key);
  //     }
  //   });

  //   // Reset to page 1 when filters change
  //   if (newFilters.search !== undefined || newFilters.status !== undefined) {
  //     params.delete('page');
  //   }

  //   startTransition(() => {
  //     router.push(`/account/service?${params.toString()}`);
  //   });
  // }, [router, searchParams]);

  const handleSearch = useCallback(() => {
    updateFilters({ search: searchTerm });
  }, [searchTerm, updateFilters]);

  const handleStatusChange = useCallback((status: string) => {
    setStatusFilter(status as ServiceFilters['status']);
    updateFilters({ status: status as ServiceFilters['status'] });
  }, [updateFilters]);

  const handlePageChange = useCallback((page: number) => {
    const params = new URLSearchParams(searchParams);
    if (page > 1) {
      params.set('page', page.toString());
    } else {
      params.delete('page');
    }
    
    startTransition(() => {
      router.push(`/account/service?${params.toString()}`);
    });
  }, [router, searchParams]);

  return (
    <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8 flex-col items-center justify-center rounded-lg text-center">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="text-left">
            <h1 className="text-2xl font-semibold">
              Cereri de Service
            </h1>
            <p className="text-muted-foreground">
              Gestionează cererile tale de service pentru produsele achiziționate
            </p>
          </div>
          <Button
            onClick={() => {
              if (availableProducts.length === 0) {
                toast.error('Nu există produse disponibile pentru service. Pentru a putea crea o cerere de service, trebuie să ai cel puțin o comandă finalizată.');
                return;
              }
              setShowNewServiceDialog(true);
            }}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Cerere nouă
          </Button>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Solicitate</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.requested}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">În lucru</CardTitle>
              <Wrench className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.processing}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Finalizate</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.completed}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filtrează cererile</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Caută după numărul cererii sau comenzii..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={statusFilter} onValueChange={handleStatusChange}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toate statusurile</SelectItem>
                    {Object.values(ServiceStatus).map((status) => {
                      const display = getServiceStatusDisplay(status);
                      return (
                        <SelectItem key={status} value={status}>
                          {display.label}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
                <Button onClick={handleSearch} disabled={isPending}>
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Service Requests List */}
        <Card>
          <CardHeader>
            <CardTitle>
              Cereri de Service ({pagination.total})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {initialServiceRequests.length === 0 ? (
              <div className="text-center py-12">
                <Wrench className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Nicio cerere de service</h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  Nu ai încă cereri de service. Creează prima ta cerere pentru produsele achiziționate.
                </p>
                <Button onClick={() => setShowNewServiceDialog(true)} className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Creează prima cerere
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {initialServiceRequests.map((serviceRequest) => (
                  <div
                    key={serviceRequest.id}
                    className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">#{serviceRequest.serviceNumber}</span>
                          <ServiceStatusBadge status={serviceRequest.status} />
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {serviceRequest.productName} • Comandă #{serviceRequest.orderNumber}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Creat la {formatDate(serviceRequest.createdAt)}
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedServiceRequest(serviceRequest)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Vezi detalii
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Pagina {pagination.currentPage} din {pagination.pages}
            </p>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.currentPage - 1)}
                disabled={!pagination.hasPrev || isPending}
              >
                <ChevronLeft className="h-4 w-4" />
                Anterior
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.currentPage + 1)}
                disabled={!pagination.hasNext || isPending}
              >
                Următor
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Service Request Details Dialog */}
        <ServiceRequestDetails
          serviceRequest={selectedServiceRequest}
          open={!!selectedServiceRequest}
          onOpenChange={(open) => !open && setSelectedServiceRequest(null)}
          onServiceRequestUpdated={() => {
            router.refresh();
          }}
        />

        {/* New Service Request Dialog */}
        <NewServiceRequestDialog
          open={showNewServiceDialog}
          onOpenChange={setShowNewServiceDialog}
          availableProducts={availableProducts}
          availableAddresses={availableAddresses}
          showrooms={showrooms}
          onServiceRequestCreated={() => {
            router.refresh();
          }}
        />
      </div>
    </div>
  );
}
